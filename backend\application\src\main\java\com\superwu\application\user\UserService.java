package com.superwu.application.user;

import com.superwu.application.user.dto.UserCreateRequest;
import com.superwu.application.user.dto.UserResponse;
import com.superwu.application.user.dto.UserUpdateRequest;
import com.superwu.domain.shared.valueobject.Id;
import com.superwu.domain.user.entity.User;
import com.superwu.domain.user.repository.UserRepository;
import com.superwu.domain.user.valueobject.Email;
import com.superwu.domain.user.valueobject.Username;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 用户应用服务
 * 处理用户相关的业务逻辑和用例
 */
@Service
public class UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserService.class);
    
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    
    public UserService(UserRepository userRepository, PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
    }
    
    /**
     * 创建用户
     */
    public Mono<UserResponse> createUser(UserCreateRequest request) {
        logger.debug("Creating user with username: {}", request.username());
        
        return validateUserNotExists(request.username(), request.email())
            .then(Mono.fromCallable(() -> {
                String hashedPassword = passwordEncoder.encode(request.password());
                return User.create(
                    Username.of(request.username()),
                    Email.of(request.email()),
                    hashedPassword
                );
            }))
            .flatMap(userRepository::save)
            .map(this::toUserResponse)
            .doOnSuccess(user -> logger.info("User created successfully: {}", user.id()))
            .doOnError(error -> logger.error("Failed to create user", error));
    }
    
    /**
     * 根据ID查找用户
     */
    public Mono<UserResponse> findById(String id) {
        return userRepository.findById(Id.of(id))
            .map(this::toUserResponse)
            .doOnSuccess(user -> logger.debug("Found user: {}", user.id()))
            .doOnError(error -> logger.error("Failed to find user by ID: {}", id, error));
    }
    
    /**
     * 根据用户名查找用户
     */
    public Mono<UserResponse> findByUsername(String username) {
        return userRepository.findByUsername(Username.of(username))
            .map(this::toUserResponse)
            .doOnSuccess(user -> logger.debug("Found user by username: {}", username))
            .doOnError(error -> logger.error("Failed to find user by username: {}", username, error));
    }
    
    /**
     * 更新用户资料
     */
    public Mono<UserResponse> updateUser(String id, UserUpdateRequest request) {
        logger.debug("Updating user: {}", id);
        
        return userRepository.findById(Id.of(id))
            .switchIfEmpty(Mono.error(new RuntimeException("User not found")))
            .map(user -> {
                user.updateProfile(request.bio(), request.githubUsername(), request.websiteUrl());
                if (request.avatarUrl() != null) {
                    user.updateAvatar(request.avatarUrl());
                }
                return user;
            })
            .flatMap(userRepository::save)
            .map(this::toUserResponse)
            .doOnSuccess(user -> logger.info("User updated successfully: {}", user.id()))
            .doOnError(error -> logger.error("Failed to update user: {}", id, error));
    }
    
    /**
     * 获取所有激活用户
     */
    public Flux<UserResponse> findAllActive() {
        return userRepository.findAllActive()
            .map(this::toUserResponse)
            .doOnComplete(() -> logger.debug("Retrieved all active users"));
    }
    
    /**
     * 验证用户不存在
     */
    private Mono<Void> validateUserNotExists(String username, String email) {
        Username usernameVO = Username.of(username);
        Email emailVO = Email.of(email);
        
        return Mono.zip(
            userRepository.existsByUsername(usernameVO),
            userRepository.existsByEmail(emailVO)
        ).flatMap(tuple -> {
            boolean usernameExists = tuple.getT1();
            boolean emailExists = tuple.getT2();
            
            if (usernameExists) {
                return Mono.error(new RuntimeException("Username already exists"));
            }
            if (emailExists) {
                return Mono.error(new RuntimeException("Email already exists"));
            }
            return Mono.empty();
        });
    }
    
    /**
     * 转换为响应DTO
     */
    private UserResponse toUserResponse(User user) {
        return new UserResponse(
            user.getId().getValue(),
            user.getUsername().getValue(),
            user.getEmail().getValue(),
            user.getAvatarUrl(),
            user.getBio(),
            user.getGithubUsername(),
            user.getWebsiteUrl(),
            user.getRole().name(),
            user.isActive(),
            user.getCreatedAt(),
            user.getUpdatedAt()
        );
    }
} 