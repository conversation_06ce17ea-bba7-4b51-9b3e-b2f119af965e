from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import AsyncGenerator, Dict, Any, List, Optional
import httpx
import json
import uuid
import os
import base64
from datetime import datetime
from pathlib import Path

from database import get_db, ContentHandler
from database import User as DBUser, ModelConfig as DBModelConfig, Conversation as DBConversation, Message as DBMessage
from chat_context import ContextManager, ChatMessage
from .models import ChatRequest, MessageRequest
from .utils import DEFAULT_USER_ID, get_model_config, save_message
from .chat_handlers import stream_chat_response, get_chat_response

router = APIRouter()

# 聊天接口
@router.post("/api/chat")
async def chat(request: ChatRequest, db: Session = Depends(get_db)):
    conversation = None
    
    try:
        # 兼容旧的字符串ID，转换为UUID
        model_id = request.modelId
        if model_id == "qwen":
            model_id = "550e8400-e29b-41d4-a716-************"
        
        # 查找模型配置
        model_config = db.query(DBModelConfig).filter(DBModelConfig.id == model_id).first()
        if not model_config:
            raise HTTPException(status_code=404, detail="模型未找到")
        
        if not model_config.enabled:
            raise HTTPException(status_code=400, detail="模型已禁用")
        
        # 获取或创建对话
        if request.conversationId:
            # 检查对话ID格式，如果不是UUID格式，直接创建新对话
            try:
                # 尝试解析为UUID，如果失败说明是前端的时间戳ID
                import uuid
                uuid.UUID(request.conversationId)
                # 如果能解析为UUID，则尝试查找
                conversation = db.query(DBConversation).filter(DBConversation.id == request.conversationId).first()
            except (ValueError, Exception) as conv_error:
                print(f"对话ID格式无效或查找失败 ('{request.conversationId}'): {conv_error}")
                conversation = None
        
        if not conversation:
            # 创建新对话
            user = db.query(DBUser).filter(DBUser.id == DEFAULT_USER_ID).first()
            if not user:
                # 如果默认用户不存在，创建一个
                user = DBUser(
                    id=DEFAULT_USER_ID,
                    username="默认用户",
                    email="<EMAIL>"
                )
                db.add(user)
                db.flush()  # 使用flush而不是commit
                print(f"创建默认用户: {user.id}")
                
            conversation = DBConversation(
                user_id=user.id,
                title=request.title or "新对话",
                model_id=model_config.id,
                system_prompt=request.systemPrompt
            )
            db.add(conversation)
            db.commit()  # 立即commit确保对话存在
            db.refresh(conversation)
            print(f"创建新对话: {conversation.id}")
        
        # 提前获取需要的数据，避免会话关闭后访问
        conversation_id = str(conversation.id)
        model_id = str(model_config.id)
        
        # 提前提取模型配置数据
        model_config_dict = {
            "id": model_id,
            "name": model_config.name,
            "provider": model_config.provider,
            "apiEndpoint": model_config.api_endpoint,
            "apiKey": model_config.api_key_encrypted,
            "model": model_config.model,
            "parameters": model_config.parameters
        }

        # 获取上下文管理器
        context = await ContextManager.get_or_create_context(conversation_id)

        # 如果是现有对话，从数据库加载历史消息到上下文
        if request.conversationId:
            # 检查上下文是否为空（说明是新会话或缓存过期）
            existing_context = await context.get_context(include_system=False)
            if not existing_context:
                # 从数据库加载历史消息
                historical_messages = db.query(DBMessage).filter(
                    DBMessage.conversation_id == conversation_id
                ).order_by(DBMessage.created_at).all()
                
                # 将历史消息添加到上下文
                for db_msg in historical_messages:
                    await context.add_message(ChatMessage(
                        role=db_msg.role,
                        content=db_msg.content,
                        timestamp=db_msg.created_at,
                        token_count=db_msg.token_count or len(db_msg.content) // 4
                    ))
        
        # 添加用户消息到上下文和数据库
        user_message = request.messages[-1]  # 获取最新的用户消息
        await context.add_message(ChatMessage(
            role=user_message.role,
            content=user_message.content,
            token_count=len(user_message.content) // 4  # 简单估算
        ))
        
        # 获取API调用的消息列表（包含历史上下文）
        api_messages = await context.get_messages_for_api(conversation.system_prompt)
        
        # 处理用户消息内容
        content_info = ContentHandler.process_content(user_message.content, "user")
        
        # 保存用户消息到数据库
        user_metadata = {
            "timestamp": datetime.now().isoformat(),
            "client_info": request.dict() if hasattr(request, 'dict') else {},
            "message_length": len(user_message.content),
            "conversation_context": f"Message {len(api_messages) + 1} in conversation",
            "content_info": content_info
        }
        
        user_msg = save_message(
            conversation_id=conversation_id,
            role=user_message.role,
            content=user_message.content,
            model_id=model_id,
            msg_metadata=user_metadata,
            db=db
        )
        
        # 返回流式或非流式响应
        if request.stream:
            return StreamingResponse(
                stream_chat_response_with_context(model_config_dict, api_messages, context, conversation_id, model_id, db),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache", 
                    "Connection": "keep-alive",
                    "Access-Control-Allow-Origin": "*",
                    "Access-Control-Allow-Headers": "*"
                }
            )
        else:
            # 非流式响应
            result = await get_chat_response_with_context(model_config_dict, api_messages, context, conversation_id, model_id, db)
            return {
                "conversationId": conversation_id,
                "content": result["content"],
                "model": request.modelId,
                "usage": result.get("usage")
            }
        
    except Exception as e:
        print(f"聊天处理错误: {e}")
        import traceback
        traceback.print_exc()
        if 'conversation' in locals() and conversation:
            try:
                db.rollback()
            except Exception as rollback_error:
                print(f"回滚失败: {rollback_error}")
        raise HTTPException(status_code=500, detail=f"聊天处理失败: {str(e)}")

async def stream_chat_response_with_context(
    model_config_dict: Dict[str, Any],
    messages: List[Dict[str, str]],
    context: Any,
    conversation_id: str,
    model_id: str,
    db: Session
) -> AsyncGenerator[str, None]:
    """带上下文管理的流式聊天响应"""
    try:
        # 先发送对话ID
        yield f"data: {json.dumps({'conversationId': conversation_id}, ensure_ascii=False)}\n\n"

        # 收集完整响应内容
        full_response = ""
        
        # 流式生成回复
        print(f"开始流式生成回复，模型配置: {model_config_dict.get('provider', 'unknown')}")
        print(f"🔍 调用 stream_chat_response，参数:")
        print(f"   - model_config_dict: {model_config_dict}")
        print(f"   - messages: {messages}")

        chunk_count = 0
        try:
            print("🚀 开始异步迭代 stream_chat_response...")
            async for chunk in stream_chat_response(model_config_dict, messages):
                chunk_count += 1
                print(f"✅ 在chat_api中收到第{chunk_count}个chunk: {chunk}")
                if chunk:
                    full_response += chunk
                    print(f"📤 发送chunk到前端: {chunk[:50]}...")
                    yield f"data: {json.dumps({'content': chunk}, ensure_ascii=False)}\n\n"
                else:
                    print("⚠️ 收到空chunk")
            print("🏁 stream_chat_response迭代完成")
        except Exception as e:
            print(f"❌ stream_chat_response异常: {e}")
            import traceback
            traceback.print_exc()

        print(f"流式响应完成，总共收到{chunk_count}个chunks，完整响应长度: {len(full_response)}")

        # 添加助手消息到上下文
        assistant_message = ChatMessage(
            role="assistant",
            content=full_response,
            token_count=len(full_response) // 4  # 简单估算
        )
        await context.add_message(assistant_message)
        
        # 保存助手消息到数据库
        try:
            assistant_metadata = {
                "timestamp": datetime.now().isoformat(),
                "response_length": len(full_response),
                "model_used": model_config_dict["model"],
                "content_info": ContentHandler.process_content(full_response, "assistant")
            }
            
            assistant_msg = save_message(
                conversation_id=conversation_id,
                role="assistant",
                content=full_response,
                model_id=model_id,
                msg_metadata=assistant_metadata,
                db=db
            )
        except Exception as save_error:
            print(f"保存助手消息失败: {save_error}")
            # 不影响流式响应的完成
        
        yield f"data: [DONE]\n\n"

    except Exception as e:
        print(f"流式聊天响应错误: {e}")
        import traceback
        traceback.print_exc()
        yield f"data: {json.dumps({'error': str(e)}, ensure_ascii=False)}\n\n"

async def get_chat_response_with_context(
    model_config_dict: Dict[str, Any],
    messages: List[Dict[str, str]],
    context: Any,
    conversation_id: str,
    model_id: str,
    db: Session
) -> Dict[str, Any]:
    """带上下文管理的非流式聊天响应"""
    try:
        # 获取回复
        response = await get_chat_response(model_config_dict, messages)
        content = response["content"]

        # 添加助手消息到上下文
        assistant_message = ChatMessage(
            role="assistant",
            content=content,
            token_count=len(content) // 4  # 简单估算
        )
        await context.add_message(assistant_message)

        # 保存助手消息到数据库
        assistant_metadata = {
            "timestamp": datetime.now().isoformat(),
            "response_length": len(content),
            "model_used": model_config_dict["model"],
            "usage": response.get("usage"),
            "content_info": ContentHandler.process_content(content, "assistant")
        }

        assistant_msg = save_message(
            conversation_id=conversation_id,
            role="assistant",
            content=content,
            model_id=model_id,
            msg_metadata=assistant_metadata,
            db=db
        )

        return response

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取聊天响应失败: {str(e)}")

# 图片上传接口
@router.post("/api/chat/upload-image")
async def upload_image(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """上传聊天图片"""
    try:
        # 检查文件类型
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="只支持图片文件")

        # 检查文件大小 (限制为10MB)
        file_content = await file.read()
        if len(file_content) > 10 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="图片文件大小不能超过10MB")

        # 创建上传目录
        upload_dir = Path("uploads/chat_images")
        upload_dir.mkdir(parents=True, exist_ok=True)

        # 生成唯一文件名
        file_extension = Path(file.filename).suffix.lower()
        if file_extension not in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
            raise HTTPException(status_code=400, detail="不支持的图片格式")

        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = upload_dir / unique_filename

        # 保存文件
        with open(file_path, "wb") as f:
            f.write(file_content)

        # 转换为base64用于前端显示
        base64_data = base64.b64encode(file_content).decode('utf-8')

        return {
            "success": True,
            "file_id": unique_filename,
            "file_path": str(file_path),
            "file_size": len(file_content),
            "content_type": file.content_type,
            "base64_data": f"data:{file.content_type};base64,{base64_data}"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"图片上传失败: {str(e)}")

# 带图片的聊天接口
@router.post("/api/chat/with-image")
async def chat_with_image(
    message: str = Form(...),
    model_id: str = Form(...),
    conversation_id: Optional[str] = Form(None),
    rag_enabled: bool = Form(False),
    repository_id: Optional[str] = Form(None),
    images: List[UploadFile] = File([]),
    db: Session = Depends(get_db)
):
    """支持图片的聊天接口"""
    try:
        # 处理上传的图片
        image_attachments = []
        for image in images:
            if image.filename:  # 确保有文件上传
                # 检查文件类型
                if not image.content_type or not image.content_type.startswith('image/'):
                    raise HTTPException(status_code=400, detail="只支持图片文件")

                # 读取图片内容
                image_content = await image.read()
                if len(image_content) > 10 * 1024 * 1024:
                    raise HTTPException(status_code=400, detail="图片文件大小不能超过10MB")

                # 保存图片
                upload_dir = Path("uploads/chat_images")
                upload_dir.mkdir(parents=True, exist_ok=True)

                file_extension = Path(image.filename).suffix.lower()
                if file_extension not in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
                    raise HTTPException(status_code=400, detail="不支持的图片格式")

                unique_filename = f"{uuid.uuid4()}{file_extension}"
                file_path = upload_dir / unique_filename

                with open(file_path, "wb") as f:
                    f.write(image_content)

                # 转换为base64
                base64_data = base64.b64encode(image_content).decode('utf-8')

                image_attachments.append({
                    "type": "image",
                    "file_id": unique_filename,
                    "file_path": str(file_path),
                    "file_size": len(image_content),
                    "content_type": image.content_type,
                    "base64_data": f"data:{image.content_type};base64,{base64_data}"
                })

        # 直接调用带附件的聊天逻辑
        return await chat_with_attachments_direct(
            message=message,
            model_id=model_id,
            conversation_id=conversation_id,
            rag_enabled=rag_enabled,
            repository_id=repository_id,
            attachments=image_attachments,
            db=db
        )

    except HTTPException:
        raise
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"图片聊天失败: {str(e)}")

async def chat_with_attachments_direct(
    message: str,
    model_id: str,
    conversation_id: str = None,
    rag_enabled: bool = False,
    repository_id: str = None,
    attachments: List[Dict] = None,
    db: Session = None
):
    """处理带附件的聊天请求"""
    conversation = None

    try:
        # 兼容旧的字符串ID，转换为UUID
        if model_id == "qwen":
            model_id = "550e8400-e29b-41d4-a716-************"

        # 查找模型配置
        model_config = db.query(DBModelConfig).filter(DBModelConfig.id == model_id).first()
        if not model_config:
            raise HTTPException(status_code=404, detail="模型未找到")

        if not model_config.enabled:
            raise HTTPException(status_code=400, detail="模型已禁用")

        # 获取或创建对话
        if conversation_id:
            try:
                uuid.UUID(conversation_id)
                conversation = db.query(DBConversation).filter(DBConversation.id == conversation_id).first()
            except (ValueError, Exception):
                conversation = None

        if not conversation:
            conversation = DBConversation(
                id=uuid.uuid4(),
                user_id=DEFAULT_USER_ID,
                title=message[:50] + ("..." if len(message) > 50 else ""),
                model_id=model_id
            )
            db.add(conversation)
            db.commit()
            db.refresh(conversation)

        # 保存用户消息（包含图片附件）
        content_type = "mixed" if attachments else "text"
        user_msg = save_message(
            conversation_id=conversation.id,
            role="user",
            content=message,
            model_id=model_id,
            content_type=content_type,
            attachments=attachments,
            db=db
        )

        # 构建消息历史（包含图片信息）
        messages = []
        db_messages = db.query(DBMessage).filter(
            DBMessage.conversation_id == conversation.id
        ).order_by(DBMessage.created_at).all()

        for msg in db_messages:
            message_content = msg.content
            # 如果有图片附件，需要特殊处理
            if msg.attachments and len(msg.attachments) > 0:
                # 对于支持多模态的模型，可以在这里处理图片
                # 目前先简单地在文本中标注有图片
                message_content = f"[图片] {message_content}"

            messages.append({
                "role": msg.role,
                "content": message_content
            })

        # 流式响应
        model_config_dict = get_model_config(model_id, db)

        # 构建RAG选项
        rag_options = {
            "enabled": rag_enabled,
            "repositoryId": repository_id
        } if rag_enabled else None

        async def generate_response():
            full_content = ""
            async for chunk in stream_chat_response(model_config_dict, messages, rag_options, db, attachments):
                full_content += chunk
                yield f"data: {json.dumps({'content': chunk}, ensure_ascii=False)}\n\n"

            # 保存助手回复
            assistant_metadata = {
                "model": model_config.name,
                "provider": model_config.provider,
                "conversation_id": str(conversation.id)
            }

            save_message(
                conversation_id=conversation.id,
                role="assistant",
                content=full_content,
                model_id=model_id,
                msg_metadata=assistant_metadata,
                db=db
            )

        return StreamingResponse(
            generate_response(),
            media_type="text/plain; charset=utf-8",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Conversation-Id": str(conversation.id)
            }
        )

    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"聊天失败: {str(e)}")
