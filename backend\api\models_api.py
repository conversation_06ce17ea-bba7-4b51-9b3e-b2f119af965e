from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from typing import List

from database import get_db
from database import User as DBUser, ModelConfig as DBModelConfig
from .models import ModelConfigRequest
from .utils import DEFAULT_USER_ID

router = APIRouter()

# 获取所有模型配置
@router.get("/api/models")
async def get_models(db: Session = Depends(get_db)):
    configs = db.query(DBModelConfig).filter(DBModelConfig.enabled == True).all()
    safe_configs = []
    for config in configs:
        safe_configs.append({
            "id": str(config.id),
            "name": config.name,
            "provider": config.provider,
            "apiEndpoint": config.api_endpoint,
            "apiKey": "••••••••",  # 不返回真实密钥
            "model": config.model,
            "enabled": config.enabled,
            "parameters": config.parameters
        })
    return safe_configs

# 添加模型配置
@router.post("/api/models")
async def add_model(model: ModelConfigRequest, db: Session = Depends(get_db)):
    # 获取默认用户
    user = db.query(DBUser).filter(DBUser.id == DEFAULT_USER_ID).first()
    if not user:
        raise HTTPException(status_code=404, detail="用户未找到")
    
    new_model = DBModelConfig(
        name=model.name,
        provider=model.provider,
        api_endpoint=model.apiEndpoint,
        api_key_encrypted=model.apiKey,  # 生产环境需要加密
        model=model.model,
        enabled=model.enabled,
        parameters=model.parameters.dict() if hasattr(model.parameters, 'dict') else model.parameters.model_dump(),
        created_by=user.id
    )
    
    db.add(new_model)
    db.commit()
    db.refresh(new_model)
    
    return {"success": True, "id": str(new_model.id)}

# 更新模型配置
@router.put("/api/models/{model_id}")
async def update_model(model_id: str, model: ModelConfigRequest, db: Session = Depends(get_db)):
    config = db.query(DBModelConfig).filter(DBModelConfig.id == model_id).first()
    if not config:
        raise HTTPException(status_code=404, detail="模型未找到")
    
    # 更新字段
    config.name = model.name
    config.provider = model.provider
    config.api_endpoint = model.apiEndpoint
    if model.apiKey and model.apiKey != "••••••••":
        config.api_key_encrypted = model.apiKey
    config.model = model.model
    config.enabled = model.enabled
    config.parameters = model.parameters.dict() if hasattr(model.parameters, 'dict') else model.parameters.model_dump()
    
    db.commit()
    return {"success": True}

# 删除模型配置
@router.delete("/api/models/{model_id}")
async def delete_model(model_id: str, db: Session = Depends(get_db)):
    config = db.query(DBModelConfig).filter(DBModelConfig.id == model_id).first()
    if not config:
        raise HTTPException(status_code=404, detail="模型未找到")
    
    db.delete(config)
    db.commit()
    return {"success": True} 