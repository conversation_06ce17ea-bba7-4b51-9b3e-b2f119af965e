-- 创建数据库初始化脚本
-- 用于设置SuperWu项目的PostgreSQL数据库

-- 创建用户
CREATE USER superwu WITH PASSWORD 'superwu123';

-- 创建数据库
CREATE DATABASE superwu_db OWNER superwu;

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE superwu_db TO superwu;

-- 连接到新创建的数据库
\c superwu_db;

-- 授予schema权限
GRANT ALL ON SCHEMA public TO superwu;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO superwu;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO superwu;

-- 确保未来创建的表也有权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO superwu;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO superwu; 