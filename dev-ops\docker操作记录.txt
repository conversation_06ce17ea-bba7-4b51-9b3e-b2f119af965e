taskkill /IM node.exe /F

python -m uvicorn main:app --port 3002 --reload

// docker启动
docker-compose up -d postgres redis

// 检查
docker exec superblog-postgres psql -U superblog -d superblog -c "SELECT version();"
docker exec superblog-redis redis-cli ping
docker exec superwu-redis redis-cli -a 123456 ping

// 启动
mvn compile exec:java

// 插入数据
docker exec superblog-postgres psql -U superblog -d superblog -c "INSERT INTO users (username, email, password_hash, bio, role) VALUES ('admin', '<EMAIL>', '\$2a\$10\$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9cPShGpfgNmLrYq', 'SuperBlog 管理员', 'ADMIN'), ('demo', '<EMAIL>', '\$2a\$10\$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9cPShGpfgNmLrYq', '演示用户', 'USER');"
INSERT 0 2

docker exec superblog-postgres psql -U superblog -d superblog -c "SELECT id, username, email, role FROM users;"

// 删除数据
docker-compose exec postgres psql -U superblog -d superblog -c "DELETE FROM image_generations;"

//运行Flyway repair命令修复校验和不匹配问题
mvn flyway:repair


=====================================
mvn spring-boot:run -pl web


Get-Process | Where-Object {$_.ProcessName -like "*node*"} | Stop-Process -Force

连接pg
docker-compose exec postgres psql -U postgres -d chatbot_db
-- 查看所有表
\dt

-- 查看用户表
SELECT * FROM users;

-- 查看模型配置表
SELECT * FROM model_configs;

-- 查看对话表
SELECT * FROM conversations LIMIT 5;

-- 查看消息表
SELECT * FROM messages LIMIT 5;

-- 查看统计表
SELECT * FROM chat_statistics LIMIT 5;

-- 添加通义千问模型配置
INSERT INTO model_configs (
    id, 
    name, 
    provider, 
    api_endpoint, 
    api_key_encrypted, 
    model, 
    enabled, 
    parameters, 
    created_by, 
    created_at, 
    updated_at
) VALUES (
    gen_random_uuid(),
    'qwen',
    'qwen',
    'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
    'sk-4606dfde828a4f9aa7a43f5d53dddb9e',
    'qwen-turbo',
    true,
    '{"temperature": 0.7, "max_tokens": 2000}',
    '550e8400-e29b-41d4-a716-************',
    NOW(),
    NOW()
);