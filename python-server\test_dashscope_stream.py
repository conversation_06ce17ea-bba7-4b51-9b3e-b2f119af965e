#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DashScope 流式API测试
"""

import asyncio
import httpx
import json

async def test_dashscope_stream():
    """测试DashScope流式API"""
    print("🚀 开始测试DashScope流式API...")
    
    url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    api_key = "sk-4606dfde828a4f9aa7a43f5d53dddb9e"
    
    # 测试1: 非流式请求
    print("\n📤 测试1: 非流式请求")
    payload_normal = {
        "model": "qwen-plus",
        "input": {
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "你好，请简单介绍一下自己。"}
            ]
        },
        "parameters": {
            "temperature": 0.7,
            "max_tokens": 200,
            "result_format": "message"
        }
    }
    
    headers_normal = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(url, json=payload_normal, headers=headers_normal)
            print(f"📊 状态码: {response.status_code}")
            print(f"📋 响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 非流式请求成功!")
                print(f"📝 响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
                if "output" in result and "choices" in result["output"]:
                    content = result["output"]["choices"][0]["message"]["content"]
                    print(f"💬 内容: {content}")
            else:
                print(f"❌ 非流式请求失败: {response.text}")
    except Exception as e:
        print(f"❌ 非流式请求异常: {e}")
    
    # 测试2: 流式请求 (方法1)
    print("\n🌊 测试2: 流式请求 (方法1 - incremental_output)")
    payload_stream1 = {
        "model": "qwen-plus",
        "input": {
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "请用一句话介绍Python。"}
            ]
        },
        "parameters": {
            "temperature": 0.7,
            "max_tokens": 100,
            "incremental_output": True,
            "result_format": "message"
        }
    }
    
    headers_stream1 = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "Accept": "text/event-stream",
        "X-DashScope-SSE": "enable"
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            async with client.stream("POST", url, json=payload_stream1, headers=headers_stream1) as response:
                print(f"📊 状态码: {response.status_code}")
                print(f"📋 响应头: {dict(response.headers)}")
                
                if response.status_code == 200:
                    line_count = 0
                    async for line in response.aiter_lines():
                        line_count += 1
                        print(f"📥 第{line_count}行: '{line}' (长度: {len(line)})")
                        
                        if line.startswith("data:"):
                            try:
                                data_str = line[5:].strip()
                                if data_str and data_str != "[DONE]":
                                    data = json.loads(data_str)
                                    print(f"📊 解析数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                            except json.JSONDecodeError as e:
                                print(f"❌ JSON解析失败: {e}")
                    
                    print(f"📈 总共收到 {line_count} 行")
                else:
                    error_text = await response.aread()
                    print(f"❌ 流式请求失败: {response.status_code} - {error_text.decode()}")
    except Exception as e:
        print(f"❌ 流式请求异常: {e}")
    
    # 测试3: 流式请求 (方法2 - 使用不同的头部)
    print("\n🌊 测试3: 流式请求 (方法2 - 简化头部)")
    headers_stream2 = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "Accept": "text/event-stream"
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            async with client.stream("POST", url, json=payload_stream1, headers=headers_stream2) as response:
                print(f"📊 状态码: {response.status_code}")
                print(f"📋 响应头: {dict(response.headers)}")
                
                if response.status_code == 200:
                    line_count = 0
                    async for line in response.aiter_lines():
                        line_count += 1
                        print(f"📥 第{line_count}行: '{line}'")
                    
                    print(f"📈 总共收到 {line_count} 行")
                    
                    # 如果没有收到行，尝试读取原始内容
                    if line_count == 0:
                        print("🔍 尝试读取原始响应内容...")
                        raw_content = await response.aread()
                        print(f"📄 原始内容: {raw_content.decode()}")
                else:
                    error_text = await response.aread()
                    print(f"❌ 流式请求失败: {response.status_code} - {error_text.decode()}")
    except Exception as e:
        print(f"❌ 流式请求异常: {e}")

async def main():
    await test_dashscope_stream()

if __name__ == "__main__":
    asyncio.run(main())
