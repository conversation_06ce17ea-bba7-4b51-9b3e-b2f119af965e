version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: chatbot-postgres
    environment:
      POSTGRES_DB: chatbot_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres123
      CHATBOT_DB: chatbot_db
      CHATBOT_USER: chatbot_user
      CHATBOT_PASSWORD: chatbot_pass
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db:/docker-entrypoint-initdb.d
    networks:
      - chatbot-network
    restart: unless-stopped

  # Redis 缓存 (用于会话管理)
  redis:
    image: redis:7-alpine
    container_name: chatbot-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - chatbot-network
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass "123456"

  # Python 后端 API
  backend:
    build:
      context: ../python-server
      dockerfile: ../dev-ops/Dockerfile.backend
    container_name: chatbot-backend
    environment:
      - DATABASE_URL=***********************************************/chatbot_db
      - REDIS_URL=redis://:123456@redis:6379
      - PORT=3002
      - DASHSCOPE_API_KEY=sk-4606dfde828a4f9aa7a43f5d53dddb9e
    ports:
      - "3002:3002"
    depends_on:
      - postgres
      - redis
    networks:
      - chatbot-network
    restart: unless-stopped
    volumes:
      - ../python-server:/app
    working_dir: /app

  # 前端应用
  frontend:
    build:
      context: ..
      dockerfile: dev-ops/Dockerfile.frontend
    container_name: chatbot-frontend
    environment:
      - VITE_API_BASE_URL=http://localhost:3002
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - chatbot-network
    restart: unless-stopped

  # pgAdmin 数据库管理界面
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: chatbot-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    depends_on:
      - postgres
    networks:
      - chatbot-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  chatbot-network:
    driver: bridge 