import redis
import json
import os
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel

# Redis连接配置
REDIS_URL = os.getenv("REDIS_URL", "redis://:123456@localhost:6379")
redis_client = redis.from_url(REDIS_URL, decode_responses=True)

class ChatMessage(BaseModel):
    role: str
    content: str
    timestamp: datetime = None
    token_count: Optional[int] = None
    msg_metadata: Dict[str, Any] = {}
    
    def __init__(self, **data):
        if 'timestamp' not in data:
            data['timestamp'] = datetime.now()
        super().__init__(**data)

class ChatContext:
    """聊天上下文管理器"""
    
    def __init__(self, conversation_id: str, max_context_length: int = 8000):
        self.conversation_id = conversation_id
        self.max_context_length = max_context_length
        self.cache_key = f"chat_context:{conversation_id}"
        self.cache_expiry = 3600 * 24  # 24小时过期
        
    async def add_message(self, message: ChatMessage) -> None:
        """添加消息到上下文"""
        try:
            # 获取当前上下文
            context = await self.get_context()
            
            # 添加新消息
            context.append(message.dict())
            
            # 控制上下文长度
            context = await self._trim_context(context)
            
            # 保存到Redis
            await self._save_context(context)
            
        except Exception as e:
            print(f"添加消息到上下文失败: {e}")
    
    async def get_context(self, include_system: bool = True) -> List[Dict[str, Any]]:
        """获取对话上下文"""
        try:
            # 从Redis获取缓存的上下文
            cached_context = redis_client.get(self.cache_key)
            if cached_context:
                context = json.loads(cached_context)
            else:
                context = []
            
            # 过滤系统消息
            if not include_system:
                context = [msg for msg in context if msg.get('role') != 'system']
            
            return context
            
        except Exception as e:
            print(f"获取上下文失败: {e}")
            return []
    
    async def get_messages_for_api(self, system_prompt: Optional[str] = None) -> List[Dict[str, str]]:
        """获取适用于API调用的消息格式"""
        context = await self.get_context(include_system=False)
        
        # 构建消息列表
        messages = []
        
        # 添加系统提示
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        # 添加历史消息（只保留role和content）
        for msg in context:
            if msg.get('role') in ['user', 'assistant']:
                messages.append({
                    "role": msg['role'],
                    "content": msg['content']
                })
        
        return messages
    
    async def clear_context(self) -> None:
        """清空上下文"""
        try:
            redis_client.delete(self.cache_key)
        except Exception as e:
            print(f"清空上下文失败: {e}")
    
    async def get_context_summary(self) -> Dict[str, Any]:
        """获取上下文摘要信息"""
        context = await self.get_context()
        
        total_messages = len(context)
        user_messages = len([msg for msg in context if msg.get('role') == 'user'])
        assistant_messages = len([msg for msg in context if msg.get('role') == 'assistant'])
        total_tokens = sum(msg.get('token_count', 0) for msg in context if msg.get('token_count'))
        
        return {
            "conversation_id": self.conversation_id,
            "total_messages": total_messages,
            "user_messages": user_messages,
            "assistant_messages": assistant_messages,
            "total_tokens": total_tokens,
            "last_activity": context[-1].get('timestamp') if context else None
        }
    
    async def _trim_context(self, context: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """修剪上下文以保持在令牌限制内"""
        total_tokens = sum(msg.get('token_count', len(msg.get('content', '')) // 4) for msg in context)
        
        # 如果超过限制，移除较早的消息（保留系统消息）
        while total_tokens > self.max_context_length and len(context) > 1:
            # 找到第一个非系统消息并移除
            for i, msg in enumerate(context):
                if msg.get('role') != 'system':
                    removed_msg = context.pop(i)
                    total_tokens -= removed_msg.get('token_count', len(removed_msg.get('content', '')) // 4)
                    break
            else:
                # 如果只剩系统消息，跳出循环
                break
        
        return context
    
    async def _save_context(self, context: List[Dict[str, Any]]) -> None:
        """保存上下文到Redis"""
        try:
            # 处理datetime序列化
            for msg in context:
                if 'timestamp' in msg and isinstance(msg['timestamp'], datetime):
                    msg['timestamp'] = msg['timestamp'].isoformat()
            
            redis_client.setex(
                self.cache_key,
                self.cache_expiry,
                json.dumps(context, ensure_ascii=False)
            )
        except Exception as e:
            print(f"保存上下文失败: {e}")

class ContextManager:
    """全局上下文管理器"""
    
    @staticmethod
    async def get_or_create_context(conversation_id: str, max_context_length: int = 8000) -> ChatContext:
        """获取或创建上下文"""
        return ChatContext(conversation_id, max_context_length)
    
    @staticmethod
    async def cleanup_expired_contexts() -> int:
        """清理过期的上下文（由后台任务调用）"""
        try:
            # 获取所有上下文键
            keys = redis_client.keys("chat_context:*")
            cleaned = 0
            
            for key in keys:
                # 检查键是否存在（可能已过期）
                if not redis_client.exists(key):
                    cleaned += 1
            
            return cleaned
            
        except Exception as e:
            print(f"清理过期上下文失败: {e}")
            return 0
    
    @staticmethod
    async def get_active_conversations() -> List[str]:
        """获取所有活跃的对话ID"""
        try:
            keys = redis_client.keys("chat_context:*")
            conversation_ids = [key.split(":", 1)[1] for key in keys]
            return conversation_ids
        except Exception as e:
            print(f"获取活跃对话失败: {e}")
            return []

# 智能上下文优化器
class ContextOptimizer:
    """上下文优化器 - 智能管理对话历史"""
    
    @staticmethod
    async def optimize_context(context: List[Dict[str, Any]], max_tokens: int = 6000) -> List[Dict[str, Any]]:
        """优化上下文，保留重要信息"""
        if not context:
            return context
        
        # 计算总令牌数
        total_tokens = sum(msg.get('token_count', len(msg.get('content', '')) // 4) for msg in context)
        
        if total_tokens <= max_tokens:
            return context
        
        # 保留策略：
        # 1. 始终保留系统消息
        # 2. 保留最近的N轮对话
        # 3. 保留重要的长对话
        
        system_messages = [msg for msg in context if msg.get('role') == 'system']
        other_messages = [msg for msg in context if msg.get('role') != 'system']
        
        # 保留最近的对话轮次
        recent_messages = other_messages[-10:]  # 最近5轮对话（用户+助手）
        
        # 合并结果
        optimized_context = system_messages + recent_messages
        
        return optimized_context
    
    @staticmethod
    def calculate_message_importance(message: Dict[str, Any]) -> float:
        """计算消息重要性分数"""
        content = message.get('content', '')
        role = message.get('role', '')
        
        importance = 0.0
        
        # 系统消息最重要
        if role == 'system':
            importance += 1.0
        
        # 较长的消息可能更重要
        if len(content) > 100:
            importance += 0.3
        
        # 包含特定关键词的消息更重要
        important_keywords = ['重要', '关键', '问题', '解决', '方案', '建议']
        for keyword in important_keywords:
            if keyword in content:
                importance += 0.2
                break
        
        return importance 