import React, { useState, useRef, useEffect } from 'react'
import { Send, Loader2, Bo<PERSON>, User, ChevronDown, ArrowUp, Database, Search } from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import useChatStore from '../store/chatStore'
import { knowledgeApi } from '../services/api'

const ChatPage = () => {
  const [inputMessage, setInputMessage] = useState('')
  const [showModelSelector, setShowModelSelector] = useState(false)
  const [showScrollToTop, setShowScrollToTop] = useState(false)
  const [showKnowledgeSelector, setShowKnowledgeSelector] = useState(false)
  const [repositories, setRepositories] = useState([])
  const [selectedRepository, setSelectedRepository] = useState(null)
  const [ragEnabled, setRagEnabled] = useState(false)
  const messagesEndRef = useRef(null)
  const messagesContainerRef = useRef(null)
  const inputRef = useRef(null)
  
  const {
    getCurrentConversation,
    sendMessage,
    isLoading,
    models,
    currentModel,
    setCurrentModel,
    createConversation,
    currentConversationId
  } = useChatStore()

  const currentConversation = getCurrentConversation()

  // 加载知识库列表
  const loadRepositories = async () => {
    try {
      const data = await knowledgeApi.getRepositories()
      setRepositories(data)
    } catch (error) {
      console.error('加载知识库失败:', error)
    }
  }

  // 滚动到最新消息
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [currentConversation?.messages])

  // 加载知识库
  useEffect(() => {
    loadRepositories()
  }, [])

  // 当对话切换时也滚动到底部
  useEffect(() => {
    if (currentConversation && messagesEndRef.current) {
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
      }, 100)
    }
  }, [currentConversationId])

  // 聚焦输入框
  useEffect(() => {
    inputRef.current?.focus()
  }, [currentConversationId])

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!inputMessage.trim() || isLoading) return

    const message = inputMessage
    setInputMessage('')
    
    // 构建RAG选项
    const ragOptions = ragEnabled ? {
      enabled: true,
      repositoryId: selectedRepository?.id
    } : null
    
    await sendMessage(message, ragOptions)
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  const startNewChat = () => {
    createConversation()
    setInputMessage('')
  }

  const scrollToTop = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({ top: 0, behavior: 'smooth' })
    }
  }

  const handleScroll = (e) => {
    const container = e.target
    const showButton = container.scrollTop > 300
    setShowScrollToTop(showButton)
  }

  const enabledModels = models.filter(m => m.enabled)

  return (
    <div className="flex-1 flex flex-col h-full">
      {/* 顶部工具栏 */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h2 className="text-lg font-semibold text-gray-800">
              {currentConversation?.title || '选择或创建新对话'}
            </h2>
            
            {!currentConversation && (
              <button
                onClick={startNewChat}
                className="btn-primary text-sm"
              >
                开始新对话
              </button>
            )}
          </div>

          <div className="flex items-center space-x-3">
            {/* RAG开关和知识库选择 */}
            <div className="flex items-center space-x-2">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={ragEnabled}
                  onChange={(e) => setRagEnabled(e.target.checked)}
                  className="sr-only"
                />
                <div className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  ragEnabled ? 'bg-blue-600' : 'bg-gray-200'
                }`}>
                  <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    ragEnabled ? 'translate-x-6' : 'translate-x-1'
                  }`} />
                </div>
                <span className="ml-2 text-sm font-medium text-gray-700">RAG</span>
              </label>
              
              {ragEnabled && (
                <div className="relative">
                  <button
                    onClick={() => setShowKnowledgeSelector(!showKnowledgeSelector)}
                    className="flex items-center space-x-2 px-3 py-2 bg-green-100 rounded-md hover:bg-green-200 transition-colors"
                  >
                    <Database className="h-4 w-4" />
                    <span className="text-sm font-medium">
                      {selectedRepository?.name || '选择知识库'}
                    </span>
                    <ChevronDown className="h-4 w-4" />
                  </button>

                  {showKnowledgeSelector && (
                    <div className="absolute left-0 mt-2 w-64 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                      <div className="py-1">
                        {repositories.length === 0 ? (
                          <div className="px-4 py-2 text-sm text-gray-500">
                            暂无知识库
                          </div>
                        ) : (
                          repositories.map((repo) => (
                            <button
                              key={repo.id}
                              onClick={() => {
                                setSelectedRepository(repo)
                                setShowKnowledgeSelector(false)
                              }}
                              className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 transition-colors ${
                                selectedRepository?.id === repo.id ? 'bg-green-50 text-green-700' : 'text-gray-700'
                              }`}
                            >
                              <div className="font-medium">{repo.name}</div>
                              <div className="text-xs text-gray-500">
                                {repo.document_count} 文档 · {repo.total_chunks} 分块
                              </div>
                            </button>
                          ))
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* 模型选择器 */}
            <div className="relative">
              <button
                onClick={() => setShowModelSelector(!showModelSelector)}
                className="flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                disabled={enabledModels.length === 0}
              >
                <Bot className="h-4 w-4" />
                <span className="text-sm font-medium">
                  {currentModel?.name || '选择模型'}
                </span>
                <ChevronDown className="h-4 w-4" />
              </button>

              {showModelSelector && (
                <div className="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                  <div className="py-1">
                    {enabledModels.length === 0 ? (
                      <div className="px-4 py-2 text-sm text-gray-500">
                        暂无可用模型
                      </div>
                    ) : (
                      enabledModels.map((model) => (
                        <button
                          key={model.id}
                          onClick={() => {
                            setCurrentModel(model)
                            setShowModelSelector(false)
                          }}
                          className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 transition-colors ${
                            currentModel?.id === model.id ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                          }`}
                        >
                          <div className="font-medium">{model.name}</div>
                          <div className="text-xs text-gray-500">{model.provider}</div>
                        </button>
                      ))
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 消息列表 */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto scrollbar-thin p-4 min-h-0 relative"
        onScroll={handleScroll}
      >
        {!currentConversation ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <Bot className="h-16 w-16 text-gray-300 mb-4" />
            <h3 className="text-xl font-medium text-gray-600 mb-2">
              欢迎使用多模型聊天机器人
            </h3>
            <p className="text-gray-500 mb-6 max-w-md">
              选择一个AI模型开始对话，或者从左侧创建新的对话。{ragEnabled && ' 当前已启用RAG模式。'}
            </p>
            <button
              onClick={startNewChat}
              className="btn-primary"
              disabled={!currentModel || (ragEnabled && !selectedRepository)}
            >
              开始新对话
            </button>
            {!currentModel && (
              <p className="text-sm text-red-500 mt-2">
                请先在模型管理中配置并启用至少一个模型
              </p>
            )}
            {ragEnabled && !selectedRepository && (
              <p className="text-sm text-red-500 mt-2">
                RAG模式需要选择一个知识库
              </p>
            )}
          </div>
        ) : (
          <div className="max-w-4xl mx-auto">
            {ragEnabled && selectedRepository && (
              <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Database className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-800">
                    RAG模式：使用知识库 "{selectedRepository.name}"
                  </span>
                </div>
              </div>
            )}
            
            {currentConversation.messages.length === 0 ? (
              <div className="text-center py-8">
                <Bot className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">开始新的对话吧！</p>
              </div>
            ) : (
              currentConversation.messages.map((message) => (
                <div
                  key={message.id}
                  className={`chat-bubble ${message.role}`}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                      message.role === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'
                    }`}>
                      {message.role === 'user' ? (
                        <User className="h-4 w-4" />
                      ) : (
                        <Bot className="h-4 w-4" />
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-sm font-medium">
                          {message.role === 'user' ? '你' : (message.model || '助手')}
                        </span>
                        {message.isStreaming && (
                          <span className="text-xs text-blue-500 flex items-center space-x-1">
                            <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse"></div>
                            <span>正在输入...</span>
                          </span>
                        )}
                        <span className="text-xs text-gray-500">
                          {new Date(message.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      
                      <div className={`prose prose-sm max-w-none ${
                        message.role === 'user' ? 'text-white' : 'text-gray-800'
                      } ${message.isError ? 'text-red-600' : ''} ${
                        message.isStreaming ? 'streaming-message' : ''
                      }`}>
                        <ReactMarkdown>{message.content}</ReactMarkdown>
                      </div>
                      
                      {message.usage && (
                        <div className="text-xs text-gray-500 mt-2">
                          Token使用: {message.usage.total_tokens || '未知'}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
            
            {isLoading && !currentConversation.messages.some(m => m.isStreaming) && (
              <div className="chat-bubble assistant">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-300 text-gray-600 flex items-center justify-center">
                    <Bot className="h-4 w-4" />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm text-gray-600">正在思考中...</span>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        )}

        {/* 回到顶部按钮 */}
        {showScrollToTop && currentConversation && (
          <button
            onClick={scrollToTop}
            className="absolute bottom-4 right-4 bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-full shadow-lg transition-all duration-200 z-10"
            title="回到顶部"
          >
            <ArrowUp className="h-4 w-4" />
          </button>
        )}
      </div>

      {/* 输入区域 */}
      {currentConversation && (
        <div className="flex-shrink-0 bg-white border-t border-gray-200 p-4">
          <form onSubmit={handleSubmit} className="max-w-4xl mx-auto">
            <div className="flex space-x-4">
              <div className="flex-1">
                <textarea
                  ref={inputRef}
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder={
                    ragEnabled 
                      ? `与知识库对话 (${selectedRepository?.name || '未选择知识库'})...` 
                      : currentModel 
                        ? `与 ${currentModel.name} 对话...` 
                        : '请先选择一个模型'
                  }
                  disabled={isLoading || !currentModel || (ragEnabled && !selectedRepository)}
                  className="input-field resize-none"
                  rows="1"
                  style={{
                    minHeight: '44px',
                    maxHeight: '120px',
                    height: 'auto'
                  }}
                  onInput={(e) => {
                    e.target.style.height = 'auto'
                    e.target.style.height = Math.min(e.target.scrollHeight, 120) + 'px'
                  }}
                />
              </div>
              <button
                type="submit"
                disabled={!inputMessage.trim() || isLoading || !currentModel || (ragEnabled && !selectedRepository)}
                className="btn-primary flex items-center space-x-2 self-end"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
                <span>发送</span>
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  )
}

export default ChatPage 