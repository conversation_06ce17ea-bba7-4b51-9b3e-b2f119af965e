import React, { useState, useRef, useEffect } from 'react'
import { Send, Loader2, Bo<PERSON>, User, ChevronDown, ArrowUp, Database, Search, ImagePlus, X, Image } from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import useChatStore from '../store/chatStore'
import { knowledgeApi } from '../services/api'

const ChatPage = () => {
  const [inputMessage, setInputMessage] = useState('')
  const [showModelSelector, setShowModelSelector] = useState(false)
  const [showScrollToTop, setShowScrollToTop] = useState(false)
  const [showKnowledgeSelector, setShowKnowledgeSelector] = useState(false)
  const [repositories, setRepositories] = useState([])
  const [selectedRepository, setSelectedRepository] = useState(null)
  const [ragEnabled, setRagEnabled] = useState(false)
  const [selectedImages, setSelectedImages] = useState([])
  const [isUploading, setIsUploading] = useState(false)
  const messagesEndRef = useRef(null)
  const messagesContainerRef = useRef(null)
  const inputRef = useRef(null)
  const fileInputRef = useRef(null)
  
  const {
    getCurrentConversation,
    sendMessage,
    isLoading,
    models,
    currentModel,
    setCurrentModel,
    createConversation,
    currentConversationId,
    conversations,
    setConversations
  } = useChatStore()

  const currentConversation = getCurrentConversation()

  // 加载知识库列表
  const loadRepositories = async () => {
    try {
      const data = await knowledgeApi.getRepositories()
      setRepositories(data)
    } catch (error) {
      console.error('加载知识库失败:', error)
    }
  }

  // 滚动到最新消息
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [currentConversation?.messages])

  // 加载知识库
  useEffect(() => {
    loadRepositories()
  }, [])

  // 当对话切换时也滚动到底部
  useEffect(() => {
    if (currentConversation && messagesEndRef.current) {
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
      }, 100)
    }
  }, [currentConversationId])

  // 聚焦输入框
  useEffect(() => {
    inputRef.current?.focus()
  }, [currentConversationId])

  // 处理图片选择
  const handleImageSelect = (e) => {
    const files = Array.from(e.target.files)
    const validFiles = files.filter(file => {
      if (!file.type.startsWith('image/')) {
        alert('只支持图片文件')
        return false
      }
      if (file.size > 10 * 1024 * 1024) {
        alert('图片文件大小不能超过10MB')
        return false
      }
      return true
    })

    // 转换为预览对象
    const imagePromises = validFiles.map(file => {
      return new Promise((resolve) => {
        const reader = new FileReader()
        reader.onload = (e) => {
          resolve({
            file,
            preview: e.target.result,
            id: Math.random().toString(36).substr(2, 9)
          })
        }
        reader.readAsDataURL(file)
      })
    })

    Promise.all(imagePromises).then(images => {
      setSelectedImages(prev => [...prev, ...images])
    })

    // 清空文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // 移除图片
  const removeImage = (imageId) => {
    setSelectedImages(prev => prev.filter(img => img.id !== imageId))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    if ((!inputMessage.trim() && selectedImages.length === 0) || isLoading) return

    const message = inputMessage
    const images = selectedImages
    setInputMessage('')
    setSelectedImages([])

    // 构建RAG选项
    const ragOptions = ragEnabled ? {
      enabled: true,
      repositoryId: selectedRepository?.id
    } : null

    // 如果有图片，使用带图片的发送方法
    if (images.length > 0) {
      await sendMessageWithImages(message, images, ragOptions)
    } else {
      await sendMessage(message, ragOptions)
    }
  }

  // 发送带图片的消息
  const sendMessageWithImages = async (message, images, ragOptions) => {
    try {
      setIsUploading(true)

      // 确保有当前对话
      let conversationId = currentConversationId
      if (!conversationId) {
        conversationId = createConversation()
      }

      // 添加用户消息到对话中
      const userMessage = {
        id: crypto.randomUUID(),
        role: 'user',
        content: message || '[图片]',
        timestamp: new Date().toISOString(),
        attachments: images.map(img => ({
          type: 'image',
          name: img.file.name,
          url: img.preview
        }))
      }

      // 创建助手消息占位符
      const assistantMessageId = crypto.randomUUID()
      const assistantMessage = {
        id: assistantMessageId,
        role: 'assistant',
        content: '',
        timestamp: new Date().toISOString(),
        isStreaming: true,
        model: currentModel.name
      }

      // 更新对话，添加用户消息和助手消息占位符
      setConversations(prev => prev.map(conv =>
        conv.id === conversationId
          ? {
              ...conv,
              messages: [...conv.messages, userMessage, assistantMessage],
              title: conv.messages.length === 0 ? (message || '图片对话').slice(0, 20) + '...' : conv.title
            }
          : conv
      ))

      // 创建FormData
      const formData = new FormData()
      formData.append('message', message || '')
      formData.append('model_id', currentModel.id)
      if (conversationId) {
        formData.append('conversation_id', conversationId)
      }
      if (ragOptions?.enabled) {
        formData.append('rag_enabled', 'true')
        formData.append('repository_id', ragOptions.repositoryId)
      }

      // 添加图片文件
      images.forEach((img, index) => {
        formData.append('images', img.file)
      })

      // 发送请求
      const response = await fetch('http://localhost:3002/api/chat/with-image', {
        method: 'POST',
        body: formData
      })

      if (!response.ok) {
        throw new Error('发送失败')
      }

      // 处理流式响应
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let fullContent = ''

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))

              if (data.content) {
                fullContent += data.content

                // 更新助手消息内容
                setConversations(prev => prev.map(conv =>
                  conv.id === conversationId
                    ? {
                        ...conv,
                        messages: conv.messages.map(msg =>
                          msg.id === assistantMessageId
                            ? { ...msg, content: fullContent }
                            : msg
                        )
                      }
                    : conv
                ))
              }
            } catch (e) {
              console.error('Parse error:', e)
            }
          }
        }
      }

      // 完成流式传输，移除streaming状态
      setConversations(prev => prev.map(conv =>
        conv.id === conversationId
          ? {
              ...conv,
              messages: conv.messages.map(msg =>
                msg.id === assistantMessageId
                  ? { ...msg, isStreaming: false }
                  : msg
              )
            }
          : conv
      ))

    } catch (error) {
      console.error('发送图片消息失败:', error)
      alert('发送失败，请重试')
    } finally {
      setIsUploading(false)
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  const startNewChat = () => {
    createConversation()
    setInputMessage('')
  }

  const scrollToTop = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({ top: 0, behavior: 'smooth' })
    }
  }

  const handleScroll = (e) => {
    const container = e.target
    const showButton = container.scrollTop > 300
    setShowScrollToTop(showButton)
  }

  const enabledModels = models.filter(m => m.enabled)

  return (
    <div className="flex-1 flex flex-col h-full">
      {/* 顶部工具栏 */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h2 className="text-lg font-semibold text-gray-800">
              {currentConversation?.title || '选择或创建新对话'}
            </h2>
            
            {!currentConversation && (
              <button
                onClick={startNewChat}
                className="btn-primary text-sm"
              >
                开始新对话
              </button>
            )}
          </div>

          <div className="flex items-center space-x-3">
            {/* RAG开关和知识库选择 */}
            <div className="flex items-center space-x-2">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={ragEnabled}
                  onChange={(e) => setRagEnabled(e.target.checked)}
                  className="sr-only"
                />
                <div className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  ragEnabled ? 'bg-blue-600' : 'bg-gray-200'
                }`}>
                  <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    ragEnabled ? 'translate-x-6' : 'translate-x-1'
                  }`} />
                </div>
                <span className="ml-2 text-sm font-medium text-gray-700">RAG</span>
              </label>
              
              {ragEnabled && (
                <div className="relative">
                  <button
                    onClick={() => setShowKnowledgeSelector(!showKnowledgeSelector)}
                    className="flex items-center space-x-2 px-3 py-2 bg-green-100 rounded-md hover:bg-green-200 transition-colors"
                  >
                    <Database className="h-4 w-4" />
                    <span className="text-sm font-medium">
                      {selectedRepository?.name || '选择知识库'}
                    </span>
                    <ChevronDown className="h-4 w-4" />
                  </button>

                  {showKnowledgeSelector && (
                    <div className="absolute left-0 mt-2 w-64 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                      <div className="py-1">
                        {repositories.length === 0 ? (
                          <div className="px-4 py-2 text-sm text-gray-500">
                            暂无知识库
                          </div>
                        ) : (
                          repositories.map((repo) => (
                            <button
                              key={repo.id}
                              onClick={() => {
                                setSelectedRepository(repo)
                                setShowKnowledgeSelector(false)
                              }}
                              className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 transition-colors ${
                                selectedRepository?.id === repo.id ? 'bg-green-50 text-green-700' : 'text-gray-700'
                              }`}
                            >
                              <div className="font-medium">{repo.name}</div>
                              <div className="text-xs text-gray-500">
                                {repo.document_count} 文档 · {repo.total_chunks} 分块
                              </div>
                            </button>
                          ))
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* 模型选择器 */}
            <div className="relative">
              <button
                onClick={() => setShowModelSelector(!showModelSelector)}
                className="flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                disabled={enabledModels.length === 0}
              >
                <Bot className="h-4 w-4" />
                <span className="text-sm font-medium">
                  {currentModel?.name || '选择模型'}
                </span>
                <ChevronDown className="h-4 w-4" />
              </button>

              {showModelSelector && (
                <div className="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                  <div className="py-1">
                    {enabledModels.length === 0 ? (
                      <div className="px-4 py-2 text-sm text-gray-500">
                        暂无可用模型
                      </div>
                    ) : (
                      enabledModels.map((model) => (
                        <button
                          key={model.id}
                          onClick={() => {
                            setCurrentModel(model)
                            setShowModelSelector(false)
                          }}
                          className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 transition-colors ${
                            currentModel?.id === model.id ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                          }`}
                        >
                          <div className="font-medium">{model.name}</div>
                          <div className="text-xs text-gray-500">{model.provider}</div>
                        </button>
                      ))
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 消息列表 */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto scrollbar-thin p-4 min-h-0 relative"
        onScroll={handleScroll}
      >
        {!currentConversation ? (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <Bot className="h-16 w-16 text-gray-300 mb-4" />
            <h3 className="text-xl font-medium text-gray-600 mb-2">
              欢迎使用多模型聊天机器人
            </h3>
            <p className="text-gray-500 mb-6 max-w-md">
              选择一个AI模型开始对话，或者从左侧创建新的对话。{ragEnabled && ' 当前已启用RAG模式。'}
            </p>
            <button
              onClick={startNewChat}
              className="btn-primary"
              disabled={!currentModel || (ragEnabled && !selectedRepository)}
            >
              开始新对话
            </button>
            {!currentModel && (
              <p className="text-sm text-red-500 mt-2">
                请先在模型管理中配置并启用至少一个模型
              </p>
            )}
            {ragEnabled && !selectedRepository && (
              <p className="text-sm text-red-500 mt-2">
                RAG模式需要选择一个知识库
              </p>
            )}
          </div>
        ) : (
          <div className="max-w-4xl mx-auto">
            {ragEnabled && selectedRepository && (
              <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Database className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-800">
                    RAG模式：使用知识库 "{selectedRepository.name}"
                  </span>
                </div>
              </div>
            )}
            
            {currentConversation.messages.length === 0 ? (
              <div className="text-center py-8">
                <Bot className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">开始新的对话吧！</p>
              </div>
            ) : (
              currentConversation.messages.map((message) => (
                <div
                  key={message.id}
                  className={`chat-bubble ${message.role}`}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                      message.role === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'
                    }`}>
                      {message.role === 'user' ? (
                        <User className="h-4 w-4" />
                      ) : (
                        <Bot className="h-4 w-4" />
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-sm font-medium">
                          {message.role === 'user' ? '你' : (message.model || '助手')}
                        </span>
                        {message.isStreaming && (
                          <span className="text-xs text-blue-500 flex items-center space-x-1">
                            <div className="w-1 h-1 bg-blue-500 rounded-full animate-pulse"></div>
                            <span>正在输入...</span>
                          </span>
                        )}
                        <span className="text-xs text-gray-500">
                          {new Date(message.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      
                      {/* 显示图片附件 */}
                      {message.attachments && message.attachments.length > 0 && (
                        <div className="mb-2">
                          <div className="grid grid-cols-2 gap-2 max-w-md">
                            {message.attachments.map((attachment, index) => (
                              attachment.type === 'image' && (
                                <div key={index} className="relative">
                                  <img
                                    src={attachment.base64_data}
                                    alt="用户上传的图片"
                                    className="w-full h-32 object-cover rounded-lg border"
                                  />
                                  <div className="absolute bottom-1 right-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded">
                                    {(attachment.file_size / 1024).toFixed(1)}KB
                                  </div>
                                </div>
                              )
                            ))}
                          </div>
                        </div>
                      )}

                      <div className={`prose prose-sm max-w-none ${
                        message.role === 'user' ? 'text-white' : 'text-gray-800'
                      } ${message.isError ? 'text-red-600' : ''} ${
                        message.isStreaming ? 'streaming-message' : ''
                      }`}>
                        <ReactMarkdown>{message.content}</ReactMarkdown>
                      </div>
                      
                      {message.usage && (
                        <div className="text-xs text-gray-500 mt-2">
                          Token使用: {message.usage.total_tokens || '未知'}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
            
            {isLoading && !currentConversation.messages.some(m => m.isStreaming) && (
              <div className="chat-bubble assistant">
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-300 text-gray-600 flex items-center justify-center">
                    <Bot className="h-4 w-4" />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm text-gray-600">正在思考中...</span>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        )}

        {/* 回到顶部按钮 */}
        {showScrollToTop && currentConversation && (
          <button
            onClick={scrollToTop}
            className="absolute bottom-4 right-4 bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-full shadow-lg transition-all duration-200 z-10"
            title="回到顶部"
          >
            <ArrowUp className="h-4 w-4" />
          </button>
        )}
      </div>

      {/* 输入区域 */}
      {currentConversation && (
        <div className="flex-shrink-0 bg-white border-t border-gray-200 p-4">
          <form onSubmit={handleSubmit} className="max-w-4xl mx-auto">
            {/* 图片预览区域 */}
            {selectedImages.length > 0 && (
              <div className="mb-3 p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-700">
                    已选择 {selectedImages.length} 张图片
                  </span>
                  <button
                    type="button"
                    onClick={() => setSelectedImages([])}
                    className="text-sm text-red-600 hover:text-red-800"
                  >
                    清空全部
                  </button>
                </div>
                <div className="grid grid-cols-4 gap-2">
                  {selectedImages.map((image) => (
                    <div key={image.id} className="relative group">
                      <img
                        src={image.preview}
                        alt="预览"
                        className="w-full h-20 object-cover rounded border"
                      />
                      <button
                        type="button"
                        onClick={() => removeImage(image.id)}
                        className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="h-3 w-3" />
                      </button>
                      <div className="absolute bottom-1 left-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded">
                        {(image.file.size / 1024).toFixed(1)}KB
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            <div className="flex space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <textarea
                    ref={inputRef}
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder={
                      ragEnabled
                        ? `与知识库对话 (${selectedRepository?.name || '未选择知识库'})...`
                        : currentModel
                          ? `与 ${currentModel.name} 对话...`
                          : '请先选择一个模型'
                    }
                    disabled={isLoading || !currentModel || (ragEnabled && !selectedRepository)}
                    className="input-field resize-none pr-12"
                    rows="1"
                    style={{
                      minHeight: '44px',
                      maxHeight: '120px',
                      height: 'auto'
                    }}
                    onInput={(e) => {
                      e.target.style.height = 'auto'
                      e.target.style.height = Math.min(e.target.scrollHeight, 120) + 'px'
                    }}
                  />

                  {/* 图片上传按钮 */}
                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isLoading || !currentModel}
                    className="absolute right-2 top-2 p-2 text-gray-400 hover:text-gray-600 transition-colors"
                    title="上传图片"
                  >
                    <ImagePlus className="h-4 w-4" />
                  </button>

                  {/* 隐藏的文件输入 */}
                  <input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleImageSelect}
                    className="hidden"
                  />
                </div>
              </div>

              <button
                type="submit"
                disabled={(!inputMessage.trim() && selectedImages.length === 0) || isLoading || !currentModel || (ragEnabled && !selectedRepository)}
                className="btn-primary flex items-center space-x-2 self-end"
              >
                {isLoading || isUploading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
                <span>{isUploading ? '上传中...' : '发送'}</span>
              </button>
            </div>
          </form>
        </div>
      )}
    </div>
  )
}

export default ChatPage 