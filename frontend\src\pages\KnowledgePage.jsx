import React, { useState, useEffect } from 'react'
import { Plus, Upload, Search, FileText, Trash2, Database, Book } from 'lucide-react'
import { knowledgeApi } from '../services/api'

const KnowledgePage = () => {
  const [repositories, setRepositories] = useState([])
  const [selectedRepo, setSelectedRepo] = useState(null)
  const [documents, setDocuments] = useState([])
  const [loading, setLoading] = useState(false)
  const [showCreateRepo, setShowCreateRepo] = useState(false)
  const [showUpload, setShowUpload] = useState(false)
  const [showSearch, setShowSearch] = useState(false)
  const [searchResults, setSearchResults] = useState([])
  const [searchQuery, setSearchQuery] = useState('')
  
  // 新建知识库表单
  const [newRepo, setNewRepo] = useState({
    name: '',
    description: '',
    is_public: false,
    chunk_size: 1000,
    chunk_overlap: 200
  })

  // 文件上传
  const [uploadFile, setUploadFile] = useState(null)

  // 加载知识库列表
  const loadRepositories = async () => {
    try {
      setLoading(true)
      const data = await knowledgeApi.getRepositories()
      setRepositories(data || [])
    } catch (error) {
      console.error('加载知识库失败:', error)
      setRepositories([])
    } finally {
      setLoading(false)
    }
  }

  // 加载文档列表
  const loadDocuments = async (repositoryId) => {
    try {
      setLoading(true)
      const data = await knowledgeApi.getDocuments(repositoryId)
      setDocuments(data)
    } catch (error) {
      console.error('加载文档失败:', error)
      setDocuments([])
    } finally {
      setLoading(false)
    }
  }

  // 创建知识库
  const handleCreateRepository = async (e) => {
    e.preventDefault()
    try {
      setLoading(true)
      await knowledgeApi.createRepository(newRepo)
      setNewRepo({ name: '', description: '', is_public: false, chunk_size: 1000, chunk_overlap: 200 })
      setShowCreateRepo(false)
      await loadRepositories()
    } catch (error) {
      console.error('创建知识库失败:', error)
      alert('创建知识库失败')
    } finally {
      setLoading(false)
    }
  }

  // 上传文档
  const handleUploadDocument = async (e) => {
    e.preventDefault()
    if (!uploadFile || !selectedRepo) return

    try {
      setLoading(true)
      await knowledgeApi.uploadDocument(selectedRepo.id, uploadFile)
      setUploadFile(null)
      setShowUpload(false)
      await loadDocuments(selectedRepo.id)
      alert('文档上传成功！')
    } catch (error) {
      console.error('上传文档失败:', error)
      alert('上传文档失败: ' + error.message)
    } finally {
      setLoading(false)
    }
  }

  // 搜索知识
  const handleSearch = async (e) => {
    e.preventDefault()
    if (!searchQuery.trim() || !selectedRepo) return

    try {
      setLoading(true)
      const results = await knowledgeApi.searchKnowledge({
        query: searchQuery,
        repository_id: selectedRepo.id,
        top_k: 5,
        threshold: 0.7
      })
      setSearchResults(results)
    } catch (error) {
      console.error('搜索失败:', error)
      alert('搜索失败: ' + error.message)
    } finally {
      setLoading(false)
    }
  }

  // 删除知识库
  const handleDeleteRepository = async (repoId) => {
    if (!confirm('确定要删除这个知识库吗？此操作不可撤销。')) return

    try {
      setLoading(true)
      await knowledgeApi.deleteRepository(repoId)
      if (selectedRepo?.id === repoId) {
        setSelectedRepo(null)
        setDocuments([])
      }
      await loadRepositories()
    } catch (error) {
      console.error('删除知识库失败:', error)
      alert('删除知识库失败: ' + error.message)
    } finally {
      setLoading(false)
    }
  }

  // 删除文档
  const handleDeleteDocument = async (docId) => {
    if (!confirm('确定要删除这个文档吗？')) return

    try {
      setLoading(true)
      await knowledgeApi.deleteDocument(docId)
      await loadDocuments(selectedRepo.id)
    } catch (error) {
      console.error('删除文档失败:', error)
      alert('删除文档失败: ' + error.message)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadRepositories()
  }, [])

  useEffect(() => {
    if (selectedRepo) {
      loadDocuments(selectedRepo.id)
    }
  }, [selectedRepo])

  return (
    <div className="flex-1 flex h-full">
      {/* 左侧：知识库列表 */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800">知识库管理</h2>
            <button
              onClick={() => setShowCreateRepo(true)}
              className="btn-primary flex items-center space-x-1 text-sm"
            >
              <Plus className="h-4 w-4" />
              <span>新建</span>
            </button>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="p-4 text-center text-gray-500">加载中...</div>
          ) : repositories.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              <Database className="h-12 w-12 mx-auto mb-2 text-gray-300" />
              <p>暂无知识库</p>
              <p className="text-xs">点击新建创建第一个知识库</p>
            </div>
          ) : (
            <div className="p-4 space-y-2">
              {repositories.map((repo) => (
                <div
                  key={repo.id}
                  className="p-3 rounded-lg border border-gray-200 hover:border-gray-300 hover:bg-gray-50 cursor-pointer transition-all"
                  onClick={() => setSelectedRepo(repo)}
                >
                  <h3 className="font-medium text-gray-900">{repo.name}</h3>
                  <p className="text-sm text-gray-600">{repo.description}</p>
                  <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                    <span>{repo.document_count || 0} 文档</span>
                    <span>{repo.total_chunks || 0} 分块</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 右侧：知识库详情 */}
      <div className="flex-1 flex flex-col">
        {!selectedRepo ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <Book className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-600 mb-2">选择知识库</h3>
              <p className="text-gray-500">从左侧选择一个知识库查看详情</p>
            </div>
          </div>
        ) : (
          <>
            {/* 顶部工具栏 */}
            <div className="bg-white border-b border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-semibold text-gray-800">{selectedRepo.name}</h2>
                  <p className="text-sm text-gray-600">{selectedRepo.description}</p>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => setShowSearch(true)}
                    className="btn-secondary flex items-center space-x-1"
                  >
                    <Search className="h-4 w-4" />
                    <span>搜索</span>
                  </button>
                  <button
                    onClick={() => setShowUpload(true)}
                    className="btn-primary flex items-center space-x-1"
                  >
                    <Upload className="h-4 w-4" />
                    <span>上传文档</span>
                  </button>
                </div>
              </div>
            </div>

            {/* 文档列表 */}
            <div className="flex-1 overflow-y-auto p-4">
              {loading ? (
                <div className="text-center py-8 text-gray-500">加载中...</div>
              ) : documents.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-600 mb-2">暂无文档</h3>
                  <p className="text-gray-500 mb-4">上传文档到此知识库开始使用</p>
                  <button
                    onClick={() => setShowUpload(true)}
                    className="btn-primary"
                  >
                    上传第一个文档
                  </button>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {documents.map((doc) => (
                    <div key={doc.id} className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <FileText className="h-5 w-5 text-blue-500" />
                          <span className="text-sm font-medium text-gray-900 truncate">
                            {doc.original_filename}
                          </span>
                        </div>
                        <button
                          onClick={() => handleDeleteDocument(doc.id)}
                          className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                          title="删除文档"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                      
                      <div className="text-xs text-gray-500 space-y-1">
                        <div>类型: {doc.file_type?.toUpperCase() || '未知'}</div>
                        <div>大小: {((doc.file_size || 0) / 1024).toFixed(1)} KB</div>
                        <div>分块: {doc.chunk_count || 0}</div>
                        <div>状态: 
                          <span className={`ml-1 $\{
                            doc.processing_status === 'completed' ? 'text-green-600' :
                            doc.processing_status === 'failed' ? 'text-red-600' :
                            'text-yellow-600'
                          }`}>
                            {doc.processing_status === 'completed' ? '已完成' :
                             doc.processing_status === 'failed' ? '失败' :
                             doc.processing_status === 'processing' ? '处理中' : '等待中'}
                          </span>
                        </div>
                        <div>上传时间: {new Date(doc.created_at).toLocaleString()}</div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </>
        )}
      </div>

      {/* 创建知识库模态框 */}
      {showCreateRepo && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">创建新知识库</h3>
            <form onSubmit={handleCreateRepository}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">名称</label>
                  <input
                    type="text"
                    required
                    value={newRepo.name}
                    onChange={(e) => setNewRepo({ ...newRepo, name: e.target.value })}
                    className="input-field"
                    placeholder="知识库名称"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">描述</label>
                  <textarea
                    value={newRepo.description}
                    onChange={(e) => setNewRepo({ ...newRepo, description: e.target.value })}
                    className="input-field"
                    placeholder="知识库描述（可选）"
                    rows="3"
                  />
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => setShowCreateRepo(false)}
                  className="btn-secondary"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="btn-primary"
                >
                  {loading ? '创建中...' : '创建'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 上传文档模态框 */}
      {showUpload && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">上传文档</h3>
            <form onSubmit={handleUploadDocument}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">选择文件</label>
                  <input
                    type="file"
                    accept=".pdf,.docx,.txt,.md"
                    onChange={(e) => setUploadFile(e.target.files[0])}
                    className="input-field"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    支持 PDF, DOCX, TXT, MD 格式文件
                  </p>
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  type="button"
                  onClick={() => {
                    setShowUpload(false)
                    setUploadFile(null)
                  }}
                  className="btn-secondary"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={loading || !uploadFile}
                  className="btn-primary"
                >
                  {loading ? '上传中...' : '上传'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* 搜索模态框 */}
      {showSearch && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] flex flex-col">
            <h3 className="text-lg font-semibold mb-4">知识搜索</h3>
            <form onSubmit={handleSearch} className="mb-4">
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="输入搜索关键词..."
                  className="input-field flex-1"
                  required
                />
                <button
                  type="submit"
                  disabled={loading}
                  className="btn-primary"
                >
                  {loading ? '搜索中...' : '搜索'}
                </button>
              </div>
            </form>

            <div className="flex-1 overflow-y-auto">
              {searchResults.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  {searchQuery ? '未找到相关内容' : '输入关键词开始搜索'}
                </div>
              ) : (
                <div className="space-y-4">
                  {searchResults.map((result, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-900">{result.document_title}</h4>
                        <span className="text-sm text-gray-500">
                          相关度: {((result.relevance_score || 0) * 100).toFixed(1)}%
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{result.content}</p>
                      <div className="text-xs text-gray-500">
                        文件: {result.document_filename} | 分块: {result.chunk_index}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="flex justify-end mt-4">
              <button
                onClick={() => {
                  setShowSearch(false)
                  setSearchQuery('')
                  setSearchResults([])
                }}
                className="btn-secondary"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default KnowledgePage 