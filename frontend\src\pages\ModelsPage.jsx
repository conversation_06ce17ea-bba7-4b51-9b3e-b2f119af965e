import React, { useState, useEffect } from 'react'
import { Plus, Edit2, Trash2, <PERSON>, EyeOff, Save, X, TestTube } from 'lucide-react'
import useChatStore from '../store/chatStore'
import api from '../services/api'

const ModelsPage = () => {
  const { models, refreshModels } = useChatStore()
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingModel, setEditingModel] = useState(null)
  const [formData, setFormData] = useState({
    name: '',
    provider: 'dashscope',
    apiEndpoint: '',
    apiKey: '',
    model: '',
    parameters: {
      temperature: 0.7,
      max_tokens: 2000,
      top_p: 0.8
    }
  })
  const [showApiKey, setShowApiKey] = useState(false)
  const [testing, setTesting] = useState(false)

  useEffect(() => {
    refreshModels()
  }, [refreshModels])

  const providerTemplates = {
    dashscope: {
      name: '通义千问',
      apiEndpoint: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
      model: 'qwen-turbo',
      parameters: { temperature: 0.7, max_tokens: 2000, top_p: 0.8 }
    },
    openai: {
      name: 'OpenAI GPT',
      apiEndpoint: 'https://api.openai.com/v1/chat/completions',
      model: 'gpt-3.5-turbo',
      parameters: { temperature: 0.7, max_tokens: 2000 }
    },
    anthropic: {
      name: 'Claude',
      apiEndpoint: 'https://api.anthropic.com/v1/messages',
      model: 'claude-3-sonnet-20240229',
      parameters: { temperature: 0.7, max_tokens: 2000 }
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      provider: 'dashscope',
      apiEndpoint: '',
      apiKey: '',
      model: '',
      parameters: {
        temperature: 0.7,
        max_tokens: 2000,
        top_p: 0.8
      }
    })
    setEditingModel(null)
    setShowAddForm(false)
    setShowApiKey(false)
  }

  const handleProviderChange = (provider) => {
    const template = providerTemplates[provider]
    setFormData(prev => ({
      ...prev,
      provider,
      name: template.name,
      apiEndpoint: template.apiEndpoint,
      model: template.model,
      parameters: template.parameters
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      if (editingModel) {
        await api.updateModel(editingModel.id, formData)
      } else {
        await api.addModel(formData)
      }
      await refreshModels()
      resetForm()
    } catch (error) {
      console.error('保存模型失败:', error)
      alert('保存失败: ' + (error.response?.data?.error || error.message))
    }
  }

  const handleEdit = (model) => {
    setEditingModel(model)
    setFormData({
      name: model.name,
      provider: model.provider,
      apiEndpoint: model.apiEndpoint,
      apiKey: '', // 不显示现有密钥
      model: model.model,
      parameters: model.parameters
    })
    setShowAddForm(true)
  }

  const handleDelete = async (id) => {
    if (confirm('确定要删除这个模型吗？')) {
      try {
        await api.deleteModel(id)
        await refreshModels()
      } catch (error) {
        console.error('删除模型失败:', error)
        alert('删除失败: ' + (error.response?.data?.error || error.message))
      }
    }
  }

  const handleToggleEnabled = async (model) => {
    try {
      await api.updateModel(model.id, { enabled: !model.enabled })
      await refreshModels()
    } catch (error) {
      console.error('更新模型状态失败:', error)
    }
  }

  const testModel = async (model) => {
    setTesting(true)
    try {
      const response = await api.sendMessage(model.id, [
        { role: 'user', content: '你好，请简单介绍一下你自己。' }
      ])
      alert(`测试成功！模型响应：\n${response.content.substring(0, 200)}...`)
    } catch (error) {
      alert(`测试失败：${error.response?.data?.error || error.message}`)
    } finally {
      setTesting(false)
    }
  }

  return (
    <div className="flex-1 p-6">
      <div className="max-w-6xl mx-auto">
        {/* 页面标题 */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">模型管理</h1>
            <p className="text-gray-600 mt-1">配置和管理你的AI模型</p>
          </div>
          <button
            onClick={() => setShowAddForm(true)}
            className="btn-primary flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>添加模型</span>
          </button>
        </div>

        {/* 模型列表 */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {models.map((model) => (
            <div key={model.id} className="model-card">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900">{model.name}</h3>
                  <p className="text-sm text-gray-500">{model.provider} • {model.model}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleToggleEnabled(model)}
                    className={`p-2 rounded-md transition-colors ${
                      model.enabled 
                        ? 'text-green-600 hover:bg-green-50' 
                        : 'text-gray-400 hover:bg-gray-50'
                    }`}
                    title={model.enabled ? '禁用模型' : '启用模型'}
                  >
                    {model.enabled ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                  </button>
                  <button
                    onClick={() => testModel(model)}
                    disabled={!model.enabled || testing}
                    className="p-2 text-blue-600 hover:bg-blue-50 rounded-md transition-colors disabled:opacity-50"
                    title="测试模型"
                  >
                    <TestTube className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleEdit(model)}
                    className="p-2 text-gray-600 hover:bg-gray-50 rounded-md transition-colors"
                    title="编辑模型"
                  >
                    <Edit2 className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(model.id)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                    title="删除模型"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">状态:</span>
                  <span className={`font-medium ${
                    model.enabled ? 'text-green-600' : 'text-gray-400'
                  }`}>
                    {model.enabled ? '已启用' : '已禁用'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">API密钥:</span>
                  <span className="text-gray-600">{model.apiKey}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">温度:</span>
                  <span className="text-gray-600">{model.parameters?.temperature || 0.7}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">最大Token:</span>
                  <span className="text-gray-600">{model.parameters?.max_tokens || 2000}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {models.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Plus className="h-16 w-16 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无模型</h3>
            <p className="text-gray-500 mb-4">添加你的第一个AI模型开始使用</p>
            <button
              onClick={() => setShowAddForm(true)}
              className="btn-primary"
            >
              添加模型
            </button>
          </div>
        )}

        {/* 添加/编辑模型表单 */}
        {showAddForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto m-4">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-gray-900">
                    {editingModel ? '编辑模型' : '添加新模型'}
                  </h2>
                  <button
                    onClick={resetForm}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <X className="h-6 w-6" />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* 模型提供商 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      模型提供商
                    </label>
                    <select
                      value={formData.provider}
                      onChange={(e) => handleProviderChange(e.target.value)}
                      className="input-field"
                    >
                      <option value="dashscope">通义千问 (DashScope)</option>
                      <option value="openai">OpenAI</option>
                      <option value="anthropic">Anthropic (Claude)</option>
                    </select>
                  </div>

                  {/* 模型名称 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      模型名称
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      className="input-field"
                      required
                    />
                  </div>

                  {/* API端点 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      API端点
                    </label>
                    <input
                      type="url"
                      value={formData.apiEndpoint}
                      onChange={(e) => setFormData(prev => ({ ...prev, apiEndpoint: e.target.value }))}
                      className="input-field"
                      required
                    />
                  </div>

                  {/* API密钥 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      API密钥
                    </label>
                    <div className="relative">
                      <input
                        type={showApiKey ? 'text' : 'password'}
                        value={formData.apiKey}
                        onChange={(e) => setFormData(prev => ({ ...prev, apiKey: e.target.value }))}
                        className="input-field pr-12"
                        placeholder={editingModel ? '留空保持不变' : '请输入API密钥'}
                        required={!editingModel}
                      />
                      <button
                        type="button"
                        onClick={() => setShowApiKey(!showApiKey)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  {/* 模型版本 */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      模型版本
                    </label>
                    <input
                      type="text"
                      value={formData.model}
                      onChange={(e) => setFormData(prev => ({ ...prev, model: e.target.value }))}
                      className="input-field"
                      placeholder="如: gpt-3.5-turbo, qwen-turbo"
                      required
                    />
                  </div>

                  {/* 模型参数 */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium text-gray-900">模型参数</h3>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          温度 (Temperature)
                        </label>
                        <input
                          type="number"
                          min="0"
                          max="2"
                          step="0.1"
                          value={formData.parameters.temperature}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            parameters: { ...prev.parameters, temperature: parseFloat(e.target.value) }
                          }))}
                          className="input-field"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          最大Token数
                        </label>
                        <input
                          type="number"
                          min="1"
                          max="8000"
                          value={formData.parameters.max_tokens}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            parameters: { ...prev.parameters, max_tokens: parseInt(e.target.value) }
                          }))}
                          className="input-field"
                        />
                      </div>
                    </div>

                    {formData.provider === 'dashscope' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Top-p
                        </label>
                        <input
                          type="number"
                          min="0"
                          max="1"
                          step="0.1"
                          value={formData.parameters.top_p || 0.8}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            parameters: { ...prev.parameters, top_p: parseFloat(e.target.value) }
                          }))}
                          className="input-field"
                        />
                      </div>
                    )}
                  </div>

                  {/* 提交按钮 */}
                  <div className="flex space-x-3 pt-6">
                    <button
                      type="submit"
                      className="btn-primary flex items-center space-x-2 flex-1"
                    >
                      <Save className="h-4 w-4" />
                      <span>{editingModel ? '保存更改' : '添加模型'}</span>
                    </button>
                    <button
                      type="button"
                      onClick={resetForm}
                      className="btn-secondary flex-1"
                    >
                      取消
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ModelsPage 