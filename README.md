# 🤖 多模型聊天机器人 v2.0

一个支持多AI模型的智能聊天机器人应用，现已全面升级支持数据库存储、智能上下文管理和Docker部署！

## ✨ 主要特性

### 🧠 AI能力
- **多模型支持**: 通义千问、OpenAI GPT、Claude等
- **流式输出**: 实时打字机效果显示
- **智能上下文**: 自动管理对话历史和上下文
- **系统提示**: 支持自定义角色和行为

### 📊 数据管理  
- **持久化存储**: PostgreSQL数据库存储对话历史
- **智能缓存**: Redis缓存提升响应速度
- **对话管理**: 完整的CRUD操作支持
- **统计分析**: 详细的使用统计和成本分析

### 🚀 部署方式
- **Docker化**: 一键启动完整环境
- **开发友好**: 支持热重载开发模式
- **生产就绪**: 包含监控、日志、备份

### 🎨 用户体验
- **现代UI**: 基于React + Tailwind的现代界面
- **中文优化**: 全中文界面和交互
- **响应式设计**: 支持桌面和移动设备
- **实时交互**: WebSocket实时通信

## 🚀 快速开始

### 🐳 Docker部署 (推荐)

```bash
# Windows
dev-ops/start-all.bat

# Linux/Mac
chmod +x dev-ops/start-all.sh
./dev-ops/start-all.sh
```

访问 http://localhost:3000 开始使用！

### 🛠️ 本地开发

```bash
# 1. 启动数据库 (可选 - 使用Docker)
cd dev-ops
docker-compose up postgres redis -d

# 2. 启动Python后端
cd python-server
pip install -r requirements.txt
python start.py

# 3. 启动前端
npm install
npm run dev
```

## 📊 服务架构

```
前端 (React) :3000
       ↓
后端 (FastAPI) :3002
       ↓
数据库 (PostgreSQL) :5432
缓存 (Redis) :6379
管理 (pgAdmin) :8080
```

## 🔧 配置说明

### 环境变量
```bash
DATABASE_URL=postgresql://chatbot_user:chatbot_pass@localhost:5432/chatbot_db
REDIS_URL=redis://localhost:6379
DASHSCOPE_API_KEY=sk-4606dfde828a4f9aa7a43f5d53dddb9e
PORT=3002
```

### 模型配置
支持以下AI模型提供商：
- **通义千问**: 阿里云大模型服务
- **OpenAI**: GPT-3.5, GPT-4等
- **Anthropic**: Claude系列模型

## 📁 项目结构

```
SuperWu/
├── 🐳 dev-ops/              # Docker部署配置
│   ├── docker-compose.yml   # 容器编排
│   ├── init-db/             # 数据库初始化
│   └── *.sh                 # 启动脚本
├── 🐍 python-server/        # Python后端
│   ├── main.py              # FastAPI应用
│   ├── database.py          # 数据库模型
│   ├── chat_context.py      # 上下文管理
│   └── requirements.txt     # 依赖列表
├── ⚛️ src/                  # React前端
│   ├── components/          # UI组件
│   ├── pages/              # 页面组件
│   ├── services/           # API服务
│   └── store/              # 状态管理
└── 🟨 server/               # Node.js后端(备用)
```

## 🎯 功能特性

### 💬 聊天功能
- [x] 多轮对话支持
- [x] 流式输出显示
- [x] 消息历史记录
- [x] 上下文智能管理
- [x] 系统提示设置

### 🔧 模型管理
- [x] 添加/编辑/删除模型
- [x] 模型参数配置
- [x] API密钥管理
- [x] 模型状态切换

### 📊 数据功能
- [x] 对话持久化存储
- [x] 聊天统计分析
- [x] 数据导入导出
- [x] 缓存优化

### 🐳 部署运维
- [x] Docker完整部署
- [x] 数据库管理界面
- [x] 日志监控
- [x] 自动备份

## 📖 使用指南

### 基本使用
1. 访问 http://localhost:3000
2. 在"模型管理"页面配置AI模型
3. 在"聊天"页面开始对话
4. 在"设置"页面自定义配置

### 高级功能
- **对话管理**: 创建、删除、搜索对话
- **系统提示**: 设置AI角色和行为
- **数据统计**: 查看使用情况和成本
- **批量操作**: 导入导出对话数据

## 🔧 开发指南

### 技术栈
- **后端**: FastAPI + SQLAlchemy + PostgreSQL + Redis
- **前端**: React + Vite + Tailwind CSS + Zustand
- **部署**: Docker + Nginx + Docker Compose

### 开发环境
```bash
# 后端开发
cd python-server
pip install -r requirements.txt
uvicorn main:app --reload --port 3002

# 前端开发
npm install
npm run dev
```

### API文档
访问 http://localhost:3002/docs 查看自动生成的API文档

## 📊 性能优化

### 数据库优化
- 合理的索引设计
- 连接池配置
- 查询优化

### 缓存策略
- Redis上下文缓存
- API响应缓存
- 静态资源缓存

### 前端优化
- 组件懒加载
- 虚拟列表
- 资源压缩

## 🔒 安全考虑

- API密钥加密存储
- 数据库连接安全
- 用户输入验证
- CORS跨域保护

## 🐛 故障排除

### 常见问题
1. **端口被占用**: 修改docker-compose.yml端口配置
2. **数据库连接失败**: 检查PostgreSQL服务状态
3. **Redis连接超时**: 确认Redis服务正常运行
4. **API调用失败**: 验证模型配置和API密钥

### 日志查看
```bash
# Docker日志
docker-compose logs -f backend
docker-compose logs -f frontend

# 应用日志
tail -f python-server/app.log
```

## 🤝 贡献

欢迎提交Issue和Pull Request！

### 贡献流程
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

MIT License

## 📞 联系

- GitHub: [项目地址]
- Email: <EMAIL>
- 微信群: 扫码加入讨论

---

## 🔄 版本历史

### v2.0.0 (当前版本)
- ✨ 新增数据库存储支持
- ✨ 新增智能上下文管理  
- ✨ 新增Docker部署方案
- ✨ 新增统计分析功能
- 🔧 重构后端架构
- 🎨 优化用户界面

### v1.0.0
- ✨ 基础多模型聊天
- ✨ 流式输出显示
- ✨ 模型配置管理

---

**🎉 开始您的AI对话之旅吧！** 