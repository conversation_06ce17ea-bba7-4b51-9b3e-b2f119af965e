# SuperWu - 个人技术展示平台

一个现代化的全栈个人技术展示平台，集成AI图片生成、视频展示、博客、项目展示等功能。

## 🎯 项目概述

SuperWu是一个基于Spring Boot + React的全栈技术展示平台，为技术人员提供展示技术能力、分享知识和作品的综合性解决方案。

### 🌟 核心特色

- 🤖 **AI图片生成工具** - 集成通义千问、DALL-E等模型，支持文生图和图生图
- 🎥 **作品集展示** - 项目展示、视频演示、GitHub集成
- 📝 **技术博客系统** - Markdown编辑、分类标签、全文搜索
- 🎨 **现代化UI** - 响应式设计、暗色主题、流畅动画
- 🔒 **安全认证** - JWT认证 

## 📱 功能模块

### 页签导航架构
```
├── 首页 (Home)
│   ├── 个人简介卡片
│   ├── 技能标签云
│   ├── 最新博客预览
│   └── 项目亮点展示
├── AI工具 (AI Tools)
│   ├── 文生图工具
│   ├── 图生图工具
│   ├── 历史记录管理
│   └── 参数调节面板
├── 作品集 (Portfolio)
│   ├── 项目展示网格
│   ├── 技术栈筛选
│   ├── 视频演示
│   └── GitHub同步
├── 博客 (Blog)
│   ├── 文章列表
│   ├── Markdown编辑器
│   ├── 分类标签系统
│   ├── 全文搜索
│   └── 评论互动
└── 关于 (About)
    ├── 个人信息
    ├── 技能树
    ├── 工作经历
    └── 联系方式
```

### 🤖 AI工具模块详细功能
- **文生图功能**
  - 多模型支持：通义千问、DALL-E、Stable Diffusion
  - 参数控制：尺寸、风格、质量、步数
  - 批量生成：一次生成多张图片
  - 实时预览：生成进度显示
- **图生图功能**
  - 图片上传：拖拽上传、粘贴上传
  - 风格转换：油画、水彩、动漫等风格
  - 局部编辑：mask编辑功能
  - 分辨率增强：AI超分辨率
- **历史管理**
  - 生成历史：分页浏览历史记录
  - 收藏功能：标记喜欢的作品
  - 批量操作：删除、导出、分享
  - 标签分类：自定义标签管理

### 🎥 作品集模块详细功能
- **项目展示**
  - 项目卡片：截图轮播、技术栈标签
  - 详情页面：详细描述、功能特性
  - 在线预览：项目演示链接
  - 代码展示：关键代码片段高亮
- **视频管理**
  - 本地上传：MP4视频上传
  - 外部链接：YouTube、B站链接支持
  - 视频播放器：自定义播放控制
  - 缩略图生成：自动生成视频封面
- **GitHub集成**
  - 仓库同步：自动获取项目信息
  - 统计展示：提交次数、语言分布
  - README同步：自动同步项目说明
  - 分支管理：展示主要分支信息

### 📝 博客模块详细功能
- **编辑器功能**
  - Markdown编辑：实时预览、语法高亮
  - 工具栏：格式化工具、表情包
  - 图片上传：拖拽上传、粘贴上传
  - 代码块：多语言语法高亮
- **文章管理**
  - 草稿系统：自动保存草稿
  - 发布管理：定时发布、状态管理
  - 分类标签：多级分类、标签云
  - SEO优化：meta标签、sitemap生成
- **互动功能**
  - 评论系统：嵌套回复、情感表达
  - 点赞收藏：文章互动统计
  - 分享功能：社交媒体分享
  - 阅读统计：浏览量、阅读时长

## 🏗️ 技术架构

### 后端技术栈
- **Java 21** - 最新LTS版本，性能优化
- **Spring Boot 3.2** - 微服务框架
- **Spring WebFlux** - 响应式编程
- **Spring AI** - AI集成框架
- **Spring Security** - 安全认证
- **PostgreSQL 16** - 主数据库
- **Redis 7** - 缓存和会话存储
- **Maven** - 依赖管理
- **Flyway** - 数据库版本控制
- **TestContainers** - 集成测试

### 前端技术栈
- **React 18** - 前端框架
- **Next.js 14** - 全栈React框架
- **TypeScript** - 类型安全
- **Tailwind CSS** - 原子化CSS
- **Zustand** - 状态管理
- **Framer Motion** - 动画库
- **React Query** - 数据获取
- **React Hook Form** - 表单管理

### DDD架构设计

```
backend/
├── application/                      # 应用层
│   ├── ai/
│   │   ├── AIImageService.java      # AI图片生成应用服务
│   │   ├── dto/                     # 数据传输对象
│   │   └── command/                 # 命令对象
│   ├── blog/
│   │   ├── BlogService.java         # 博客应用服务
│   │   ├── dto/                     
│   │   └── command/                 
│   ├── portfolio/
│   │   ├── PortfolioService.java    # 作品集应用服务
│   │   ├── dto/                     
│   │   └── command/                 
│   └── user/
│       ├── UserService.java         # 用户应用服务
│       ├── dto/                     
│       └── command/                 
├── domain/                           # 领域层
│   ├── ai/
│   │   ├── entity/
│   │   │   ├── AIImage.java         # AI图片实体
│   │   │   └── GenerationTask.java  # 生成任务实体
│   │   ├── repository/
│   │   │   └── AIImageRepository.java
│   │   ├── service/
│   │   │   └── AIImageDomainService.java
│   │   └── valueobject/
│   │       ├── ImagePrompt.java     # 提示词值对象
│   │       └── GenerationParams.java
│   ├── blog/
│   │   ├── entity/
│   │   │   ├── BlogPost.java        # 博客文章实体
│   │   │   ├── Comment.java         # 评论实体
│   │   │   └── Tag.java             # 标签实体
│   │   ├── repository/
│   │   │   ├── BlogPostRepository.java
│   │   │   └── CommentRepository.java
│   │   ├── service/
│   │   │   └── BlogDomainService.java
│   │   └── valueobject/
│   │       ├── PostContent.java     # 文章内容值对象
│   │       └── PostMetadata.java    # 文章元数据
│   ├── portfolio/
│   │   ├── entity/
│   │   │   ├── Project.java         # 项目实体
│   │   │   └── ProjectVideo.java    # 项目视频实体
│   │   ├── repository/
│   │   │   └── ProjectRepository.java
│   │   ├── service/
│   │   │   └── PortfolioDomainService.java
│   │   └── valueobject/
│   │       ├── TechStack.java       # 技术栈值对象
│   │       └── ProjectUrl.java      # 项目链接值对象
│   ├── user/
│   │   ├── entity/
│   │   │   └── User.java            # 用户实体
│   │   ├── repository/
│   │   │   └── UserRepository.java
│   │   └── valueobject/
│   │       ├── Email.java           # 邮箱值对象
│   │       └── Username.java        # 用户名值对象
│   └── shared/
│       ├── entity/
│       │   └── BaseEntity.java      # 基础实体
│       ├── valueobject/
│       │   ├── Id.java              # ID值对象
│       │   └── CreatedAt.java       # 创建时间值对象
│       └── exception/
│           └── DomainException.java # 领域异常
├── infrastructure/                   # 基础设施层
│   ├── config/
│   │   ├── DatabaseConfig.java      # 数据库配置
│   │   ├── RedisConfig.java         # Redis配置
│   │   ├── SecurityConfig.java      # 安全配置
│   │   └── AIConfig.java            # AI服务配置
│   ├── persistence/
│   │   ├── ai/
│   │   │   ├── AIImageJpaRepository.java
│   │   │   └── AIImageRepositoryImpl.java
│   │   ├── blog/
│   │   │   ├── BlogPostJpaRepository.java
│   │   │   └── BlogPostRepositoryImpl.java
│   │   ├── portfolio/
│   │   │   ├── ProjectJpaRepository.java
│   │   │   └── ProjectRepositoryImpl.java
│   │   └── user/
│   │       ├── UserJpaRepository.java
│   │       └── UserRepositoryImpl.java
│   ├── external/
│   │   ├── ai/
│   │   │   ├── TongYiAIService.java  # 通义千问服务
│   │   │   └── OpenAIService.java    # OpenAI服务
│   │   ├── storage/
│   │   │   └── OSSService.java       # 对象存储服务
│   │   └── github/
│   │       └── GitHubService.java    # GitHub API服务
│   └── cache/
│       ├── BlogCacheService.java     # 博客缓存服务
│       └── UserCacheService.java     # 用户缓存服务
└── web/                              # 接口层
    ├── controller/
    │   ├── AIController.java         # AI工具控制器
    │   ├── BlogController.java       # 博客控制器
    │   ├── PortfolioController.java  # 作品集控制器
    │   └── UserController.java       # 用户控制器
    ├── dto/
    │   ├── request/                  # 请求DTO
    │   └── response/                 # 响应DTO
    ├── security/
    │   ├── JwtAuthenticationFilter.java
    │   └── SecurityService.java
    └── exception/
        └── GlobalExceptionHandler.java
```

### 前端架构设计

```
frontend/
├── src/
│   ├── app/                          # Next.js App Router
│   │   ├── (auth)/                   # 认证路由组
│   │   │   ├── login/
│   │   │   └── register/
│   │   ├── ai-tools/                 # AI工具页面
│   │   │   ├── text-to-image/
│   │   │   ├── image-to-image/
│   │   │   └── history/
│   │   ├── portfolio/                # 作品集页面
│   │   │   ├── projects/
│   │   │   └── videos/
│   │   ├── blog/                     # 博客页面
│   │   │   ├── posts/
│   │   │   ├── editor/
│   │   │   └── [slug]/
│   │   ├── about/                    # 关于页面
│   │   ├── layout.tsx                # 根布局
│   │   ├── page.tsx                  # 首页
│   │   └── globals.css               # 全局样式
│   ├── components/                   # 共享组件
│   │   ├── ai/
│   │   │   ├── ImageGenerator.tsx    # 图片生成器
│   │   │   ├── PromptInput.tsx       # 提示词输入
│   │   │   ├── ImageGallery.tsx      # 图片画廊
│   │   │   └── ParameterPanel.tsx    # 参数面板
│   │   ├── blog/
│   │   │   ├── PostList.tsx          # 文章列表
│   │   │   ├── PostCard.tsx          # 文章卡片
│   │   │   ├── MarkdownEditor.tsx    # Markdown编辑器
│   │   │   ├── CommentSection.tsx    # 评论区
│   │   │   └── TagCloud.tsx          # 标签云
│   │   ├── portfolio/
│   │   │   ├── ProjectGrid.tsx       # 项目网格
│   │   │   ├── ProjectCard.tsx       # 项目卡片
│   │   │   ├── VideoPlayer.tsx       # 视频播放器
│   │   │   └── TechStackFilter.tsx   # 技术栈筛选
│   │   ├── ui/                       # 基础UI组件
│   │   │   ├── Button.tsx
│   │   │   ├── Input.tsx
│   │   │   ├── Modal.tsx
│   │   │   ├── Toast.tsx
│   │   │   └── Loading.tsx
│   │   └── layout/
│   │       ├── Header.tsx            # 页头组件
│   │       ├── Sidebar.tsx           # 侧边栏
│   │       ├── Footer.tsx            # 页脚
│   │       └── Navigation.tsx        # 导航组件
│   ├── stores/                       # Zustand状态管理
│   │   ├── authStore.ts              # 认证状态
│   │   ├── aiStore.ts                # AI工具状态
│   │   ├── blogStore.ts              # 博客状态
│   │   └── portfolioStore.ts         # 作品集状态
│   ├── hooks/                        # 自定义Hooks
│   │   ├── useAuth.ts                # 认证Hook
│   │   ├── useApi.ts                 # API调用Hook
│   │   └── useLocalStorage.ts        # 本地存储Hook
│   ├── services/                     # API服务
│   │   ├── api.ts                    # API基础配置
│   │   ├── authService.ts            # 认证服务
│   │   ├── aiService.ts              # AI服务
│   │   ├── blogService.ts            # 博客服务
│   │   └── portfolioService.ts       # 作品集服务
│   ├── types/                        # TypeScript类型定义
│   │   ├── auth.ts
│   │   ├── ai.ts
│   │   ├── blog.ts
│   │   └── portfolio.ts
│   └── utils/                        # 工具函数
│       ├── constants.ts              # 常量定义
│       ├── helpers.ts                # 辅助函数
│       ├── validation.ts             # 表单验证
│       └── formatters.ts             # 格式化函数
├── public/                           # 静态资源
│   ├── images/
│   ├── icons/
│   └── videos/
├── docs/                             # 文档
│   ├── API.md                        # API文档
│   ├── DEPLOYMENT.md                 # 部署文档
│   └── DEVELOPMENT.md                # 开发文档
└── tests/                            # 测试文件
    ├── components/
    ├── services/
    └── utils/
```

## 💾 数据库设计

### 核心表结构

```sql
-- 用户表
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    avatar_url TEXT,
    bio TEXT,
    github_username VARCHAR(50),
    website_url TEXT,
    role VARCHAR(20) DEFAULT 'USER',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- AI图片生成记录
CREATE TABLE ai_images (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    prompt TEXT NOT NULL,
    negative_prompt TEXT,
    image_url TEXT NOT NULL,
    thumbnail_url TEXT,
    model_name VARCHAR(50) NOT NULL,
    parameters JSONB NOT NULL,
    generation_time INTEGER, -- 生成耗时(秒)
    is_favorite BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT TRUE,
    tags TEXT[],
    file_size BIGINT,
    image_width INTEGER,
    image_height INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- AI生成任务队列
CREATE TABLE ai_generation_tasks (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    task_type VARCHAR(20) NOT NULL, -- text_to_image, image_to_image
    parameters JSONB NOT NULL,
    status VARCHAR(20) DEFAULT 'PENDING', -- PENDING, PROCESSING, COMPLETED, FAILED
    progress INTEGER DEFAULT 0,
    result_image_id BIGINT REFERENCES ai_images(id),
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP
);

-- 博客文章
CREATE TABLE blog_posts (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    cover_image_url TEXT,
    status VARCHAR(20) DEFAULT 'DRAFT', -- DRAFT, PUBLISHED, ARCHIVED
    is_featured BOOLEAN DEFAULT FALSE,
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    reading_time INTEGER, -- 预计阅读时间(分钟)
    seo_title VARCHAR(200),
    seo_description TEXT,
    published_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 博客标签
CREATE TABLE blog_tags (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    slug VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    color VARCHAR(7), -- hex颜色值
    post_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 博客文章标签关系
CREATE TABLE blog_post_tags (
    post_id BIGINT REFERENCES blog_posts(id) ON DELETE CASCADE,
    tag_id BIGINT REFERENCES blog_tags(id) ON DELETE CASCADE,
    PRIMARY KEY (post_id, tag_id)
);

-- 博客评论
CREATE TABLE blog_comments (
    id BIGSERIAL PRIMARY KEY,
    post_id BIGINT REFERENCES blog_posts(id) ON DELETE CASCADE,
    parent_id BIGINT REFERENCES blog_comments(id) ON DELETE CASCADE,
    author_name VARCHAR(100) NOT NULL,
    author_email VARCHAR(100) NOT NULL,
    author_website TEXT,
    content TEXT NOT NULL,
    is_approved BOOLEAN DEFAULT FALSE,
    like_count INTEGER DEFAULT 0,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 作品集项目
CREATE TABLE portfolio_projects (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    short_description VARCHAR(300),
    tech_stack TEXT[] NOT NULL,
    github_url TEXT,
    demo_url TEXT,
    documentation_url TEXT,
    project_type VARCHAR(50), -- web, mobile, desktop, api
    status VARCHAR(20) DEFAULT 'COMPLETED', -- IN_PROGRESS, COMPLETED, ARCHIVED
    featured_image_url TEXT,
    images TEXT[],
    is_featured BOOLEAN DEFAULT FALSE,
    view_count INTEGER DEFAULT 0,
    like_count INTEGER DEFAULT 0,
    star_count INTEGER DEFAULT 0, -- 来自GitHub
    display_order INTEGER DEFAULT 0,
    start_date DATE,
    end_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 项目视频
CREATE TABLE project_videos (
    id BIGSERIAL PRIMARY KEY,
    project_id BIGINT REFERENCES portfolio_projects(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    video_url TEXT NOT NULL,
    thumbnail_url TEXT,
    video_type VARCHAR(20) NOT NULL, -- UPLOAD, YOUTUBE, BILIBILI
    duration INTEGER, -- 视频时长(秒)
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- GitHub仓库同步记录
CREATE TABLE github_repositories (
    id BIGSERIAL PRIMARY KEY,
    project_id BIGINT REFERENCES portfolio_projects(id) ON DELETE CASCADE,
    github_id BIGINT UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    full_name VARCHAR(400) NOT NULL,
    description TEXT,
    language VARCHAR(50),
    languages JSONB, -- 语言分布
    stars_count INTEGER DEFAULT 0,
    forks_count INTEGER DEFAULT 0,
    issues_count INTEGER DEFAULT 0,
    default_branch VARCHAR(100),
    is_private BOOLEAN DEFAULT FALSE,
    last_commit_at TIMESTAMP,
    last_sync_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 文件上传记录
CREATE TABLE file_uploads (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE SET NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64), -- SHA-256哈希
    upload_type VARCHAR(50), -- AVATAR, AI_IMAGE, PROJECT_IMAGE, BLOG_IMAGE, VIDEO
    related_entity_id BIGINT, -- 关联实体ID
    related_entity_type VARCHAR(50), -- 关联实体类型
    is_temp BOOLEAN DEFAULT FALSE, -- 是否临时文件
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 系统配置
CREATE TABLE system_configs (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    description TEXT,
    config_type VARCHAR(20) DEFAULT 'STRING', -- STRING, NUMBER, BOOLEAN, JSON
    is_public BOOLEAN DEFAULT FALSE, -- 是否可公开访问
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX idx_ai_images_user_id ON ai_images(user_id);
CREATE INDEX idx_ai_images_created_at ON ai_images(created_at DESC);
CREATE INDEX idx_ai_images_is_favorite ON ai_images(is_favorite) WHERE is_favorite = true;

CREATE INDEX idx_blog_posts_status ON blog_posts(status);
CREATE INDEX idx_blog_posts_published_at ON blog_posts(published_at DESC) WHERE status = 'PUBLISHED';
CREATE INDEX idx_blog_posts_view_count ON blog_posts(view_count DESC);

CREATE INDEX idx_blog_comments_post_id ON blog_comments(post_id);
CREATE INDEX idx_blog_comments_parent_id ON blog_comments(parent_id);

CREATE INDEX idx_portfolio_projects_featured ON portfolio_projects(is_featured) WHERE is_featured = true;
CREATE INDEX idx_portfolio_projects_display_order ON portfolio_projects(display_order);

CREATE INDEX idx_file_uploads_user_id ON file_uploads(user_id);
CREATE INDEX idx_file_uploads_type ON file_uploads(upload_type);
```

## 🔗 REST API设计

### 认证接口
```java
@RestController
@RequestMapping("/api/auth")
public class AuthController {
    
    @PostMapping("/login")
    public Mono<LoginResponse> login(@RequestBody LoginRequest request);
    
    @PostMapping("/register")
    public Mono<RegisterResponse> register(@RequestBody RegisterRequest request);
    
    @PostMapping("/refresh")
    public Mono<TokenResponse> refreshToken(@RequestBody RefreshTokenRequest request);
    
    @PostMapping("/logout")
    public Mono<Void> logout(@RequestHeader("Authorization") String token);
}
```

### AI工具接口
```java
@RestController
@RequestMapping("/api/ai")
public class AIController {
    
    @PostMapping("/text-to-image")
    public Mono<AIImageResponse> generateImageFromText(@RequestBody TextToImageRequest request);
    
    @PostMapping("/image-to-image")
    public Mono<AIImageResponse> generateImageFromImage(@RequestBody ImageToImageRequest request);
    
    @GetMapping("/images")
    public Flux<AIImageResponse> getUserImages(
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "20") int size,
        @RequestParam(required = false) Boolean isFavorite
    );
    
    @PatchMapping("/images/{id}/favorite")
    public Mono<Void> toggleImageFavorite(@PathVariable Long id);
    
    @DeleteMapping("/images/{id}")
    public Mono<Void> deleteImage(@PathVariable Long id);
    
    @GetMapping("/tasks/{id}")
    public Mono<GenerationTaskResponse> getTaskStatus(@PathVariable Long id);
    
    @GetMapping("/models")
    public Flux<AIModelResponse> getAvailableModels();
}
```

### 博客接口
```java
@RestController
@RequestMapping("/api/blog")
public class BlogController {
    
    @GetMapping("/posts")
    public Flux<BlogPostSummaryResponse> getPosts(
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "10") int size,
        @RequestParam(required = false) String tag,
        @RequestParam(required = false) String search,
        @RequestParam(defaultValue = "PUBLISHED") String status
    );
    
    @GetMapping("/posts/{slug}")
    public Mono<BlogPostDetailResponse> getPostBySlug(@PathVariable String slug);
    
    @PostMapping("/posts")
    public Mono<BlogPostResponse> createPost(@RequestBody CreatePostRequest request);
    
    @PutMapping("/posts/{id}")
    public Mono<BlogPostResponse> updatePost(@PathVariable Long id, @RequestBody UpdatePostRequest request);
    
    @DeleteMapping("/posts/{id}")
    public Mono<Void> deletePost(@PathVariable Long id);
    
    @PostMapping("/posts/{id}/publish")
    public Mono<Void> publishPost(@PathVariable Long id);
    
    @GetMapping("/tags")
    public Flux<TagResponse> getAllTags();
    
    @PostMapping("/posts/{postId}/comments")
    public Mono<CommentResponse> addComment(@PathVariable Long postId, @RequestBody AddCommentRequest request);
    
    @GetMapping("/posts/{postId}/comments")
    public Flux<CommentResponse> getComments(@PathVariable Long postId);
    
    @PostMapping("/upload-image")
    public Mono<ImageUploadResponse> uploadImage(@RequestPart("file") Mono<FilePart> filePart);
}
```

### 作品集接口
```java
@RestController
@RequestMapping("/api/portfolio")
public class PortfolioController {
    
    @GetMapping("/projects")
    public Flux<ProjectSummaryResponse> getProjects(
        @RequestParam(defaultValue = "0") int page,
        @RequestParam(defaultValue = "12") int size,
        @RequestParam(required = false) String[] techStack,
        @RequestParam(required = false) String projectType
    );
    
    @GetMapping("/projects/{slug}")
    public Mono<ProjectDetailResponse> getProjectBySlug(@PathVariable String slug);
    
    @PostMapping("/projects")
    public Mono<ProjectResponse> createProject(@RequestBody CreateProjectRequest request);
    
    @PutMapping("/projects/{id}")
    public Mono<ProjectResponse> updateProject(@PathVariable Long id, @RequestBody UpdateProjectRequest request);
    
    @DeleteMapping("/projects/{id}")
    public Mono<Void> deleteProject(@PathVariable Long id);
    
    @PostMapping("/projects/{projectId}/videos")
    public Mono<VideoResponse> addProjectVideo(@PathVariable Long projectId, @RequestBody AddVideoRequest request);
    
    @PostMapping("/github/sync")
    public Mono<Void> syncGitHubRepositories();
    
    @GetMapping("/github/repositories")
    public Flux<GitHubRepositoryResponse> getGitHubRepositories();
    
    @PostMapping("/upload-video")
    public Mono<VideoUploadResponse> uploadVideo(@RequestPart("file") Mono<FilePart> filePart);
}
```

## 🚀 开发路线图

### Phase 1: 项目初始化 (1周)
- [ ] 创建Spring Boot项目骨架
- [ ] 配置Maven多模块结构
- [ ] 配置PostgreSQL和Redis
- [ ] 设置Flyway数据库迁移
- [ ] 创建Next.js前端项目
- [ ] 配置Tailwind CSS和TypeScript
- [ ] 设置基础的CI/CD流水线

### Phase 2: 用户认证系统 (1-2周)
- [ ] 实现JWT认证机制
- [ ] 用户注册/登录功能
- [ ] 密码加密和验证
- [ ] 角色权限管理
- [ ] 前端认证状态管理
- [ ] 受保护路由实现

### Phase 3: 基础设施模块 (1-2周)
- [ ] 文件上传服务(本地/OSS)
- [ ] 图片处理和缩略图生成
- [ ] Redis缓存集成
- [ ] 全局异常处理
- [ ] API响应格式统一
- [ ] 日志系统配置

### Phase 4: 博客模块 (2-3周)
- [ ] 博客文章CRUD操作
- [ ] Markdown编辑器集成
- [ ] 文章分类和标签系统
- [ ] 评论系统实现
- [ ] 全文搜索功能
- [ ] SEO优化
- [ ] 文章统计和分析

### Phase 5: AI工具模块 (2-3周)
- [ ] 集成通义千问API
- [ ] 文生图功能实现
- [ ] 图生图功能实现
- [ ] 异步任务队列
- [ ] 图片历史管理
- [ ] 参数调节界面
- [ ] 批量操作功能

### Phase 6: 作品集模块 (2周)
- [ ] 项目展示CRUD
- [ ] 视频上传和播放
- [ ] GitHub API集成
- [ ] 技术栈筛选
- [ ] 项目详情页面
- [ ] 统计数据展示

### Phase 7: UI/UX优化 (1-2周)
- [ ] 响应式设计完善
- [ ] 暗色主题实现
- [ ] 动画效果添加
- [ ] 性能优化
- [ ] 无障碍访问支持
- [ ] 多语言支持准备

### Phase 8: 测试和部署 (1-2周)
- [ ] 单元测试覆盖
- [ ] 集成测试
- [ ] E2E测试
- [ ] Docker容器化
- [ ] 生产环境部署
- [ ] 监控和告警配置

## 📊 技术选型说明

### 后端选型理由
- **Java 21**: 最新LTS版本，性能提升，现代语法特性
- **Spring WebFlux**: 响应式编程，高并发处理能力
- **Spring AI**: 简化AI服务集成，统一API接口
- **PostgreSQL**: 强大的关系型数据库，支持JSON和全文搜索
- **Redis**: 高性能缓存，会话存储
- **Maven**: 成熟的依赖管理工具

### 前端选型理由
- **Next.js 14**: App Router，服务端渲染，性能优化
- **TypeScript**: 类型安全，减少运行时错误
- **Tailwind CSS**: 原子化CSS，快速开发，一致性好
- **Zustand**: 轻量级状态管理，简单易用
- **Framer Motion**: 流畅动画，增强用户体验

## 📝 开发规范

### 代码规范
- 后端使用Google Java Style
- 前端使用Prettier + ESLint
- 提交信息遵循Conventional Commits
- 单元测试覆盖率不低于80%

### API设计规范
- RESTful API设计
- 统一响应格式
- 完整的错误处理
- API版本控制
- 详细的接口文档

### 数据库规范
- 命名采用下划线风格
- 所有表必须有主键
- 外键约束完整
- 适当的索引优化

## 🔧 环境要求

### 开发环境
- JDK 21+
- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- Maven 3.8+

### 生产环境
- 4核8G内存以上
- PostgreSQL集群
- Redis集群
- CDN支持
- HTTPS证书