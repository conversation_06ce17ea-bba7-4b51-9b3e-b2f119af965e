# 修复 Node.js 弃用警告

## 问题描述
出现警告：`(node:48208) [DEP0060] DeprecationWarning: The 'util._extend' API is deprecated. Please use Object.assign() instead.`

## 解决方案

### 方案1：抑制弃用警告（推荐）
已经修改了 `package.json` 中的脚本：

```bash
# 使用无弃用警告的开发模式
npm run dev

# 如果需要看到弃用警告（调试用）
npm run dev:verbose
```

### 方案2：更新依赖包
```bash
# 删除旧的依赖
rm -rf node_modules package-lock.json

# 清理缓存
npm cache clean --force

# 重新安装
npm install
```

### 方案3：使用环境变量
在启动前端时设置环境变量：

**Windows:**
```cmd
set NODE_NO_WARNINGS=1 && npm run dev
```

**Linux/Mac:**
```bash
NODE_NO_WARNINGS=1 npm run dev
```

### 方案4：检查具体的问题包
```bash
# 查看哪个包导致的警告
npm ls --depth=0

# 审计依赖
npm audit

# 自动修复
npm audit fix
```

## 已做的修改

1. **更新了 package.json 中的依赖版本**到最新稳定版
2. **修改了启动脚本**使用 `--no-deprecation` 标志
3. **创建了 .npmrc 配置文件**来管理npm行为

## 验证修复
运行以下命令验证警告是否消失：
```bash
npm run dev
```

如果仍有问题，可以运行：
```bash
npm run dev:verbose
```
来查看详细的警告信息。
