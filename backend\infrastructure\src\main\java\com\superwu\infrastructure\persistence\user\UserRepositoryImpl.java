package com.superwu.infrastructure.persistence.user;

import com.superwu.domain.shared.valueobject.Id;
import com.superwu.domain.user.entity.User;
import com.superwu.domain.user.repository.UserRepository;
import com.superwu.domain.user.valueobject.Email;
import com.superwu.domain.user.valueobject.Username;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;

/**
 * 用户Repository实现
 * 使用R2DBC进行数据访问
 */
@Repository
public class UserRepositoryImpl implements UserRepository {
    
    private static final Logger logger = LoggerFactory.getLogger(UserRepositoryImpl.class);
    
    private final DatabaseClient databaseClient;
    
    public UserRepositoryImpl(DatabaseClient databaseClient) {
        this.databaseClient = databaseClient;
    }
    
    @Override
    public Mono<User> save(User user) {
        if (user.isNew()) {
            return insert(user);
        } else {
            return update(user);
        }
    }
    
    private Mono<User> insert(User user) {
        String sql = """
            INSERT INTO users (username, email, password_hash, avatar_url, bio, 
                             github_username, website_url, role, is_active, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING id
            """;
            
        return databaseClient.sql(sql)
            .bind("$1", user.getUsername().getValue())
            .bind("$2", user.getEmail().getValue())
            .bind("$3", user.getPasswordHash())
            .bind("$4", user.getAvatarUrl())
            .bind("$5", user.getBio())
            .bind("$6", user.getGithubUsername())
            .bind("$7", user.getWebsiteUrl())
            .bind("$8", user.getRole().name())
            .bind("$9", user.isActive())
            .bind("$10", user.getCreatedAt())
            .bind("$11", user.getUpdatedAt())
            .map(row -> {
                Long id = row.get("id", Long.class);
                user.setId(Id.of(id));
                return user;
            })
            .one()
            .doOnSuccess(savedUser -> logger.debug("User saved with ID: {}", savedUser.getId()))
            .doOnError(error -> logger.error("Failed to save user", error));
    }
    
    private Mono<User> update(User user) {
        String sql = """
            UPDATE users SET username = $1, email = $2, password_hash = $3, avatar_url = $4,
                           bio = $5, github_username = $6, website_url = $7, role = $8,
                           is_active = $9, updated_at = $10
            WHERE id = $11
            """;
            
        return databaseClient.sql(sql)
            .bind("$1", user.getUsername().getValue())
            .bind("$2", user.getEmail().getValue())
            .bind("$3", user.getPasswordHash())
            .bind("$4", user.getAvatarUrl())
            .bind("$5", user.getBio())
            .bind("$6", user.getGithubUsername())
            .bind("$7", user.getWebsiteUrl())
            .bind("$8", user.getRole().name())
            .bind("$9", user.isActive())
            .bind("$10", LocalDateTime.now())
            .bind("$11", Long.parseLong(user.getId().getValue()))
            .then()
            .thenReturn(user)
            .doOnSuccess(updatedUser -> logger.debug("User updated with ID: {}", updatedUser.getId()))
            .doOnError(error -> logger.error("Failed to update user", error));
    }
    
    @Override
    public Mono<User> findById(Id id) {
        String sql = "SELECT * FROM users WHERE id = $1";
        
        return databaseClient.sql(sql)
            .bind("$1", Long.parseLong(id.getValue()))
            .map(this::mapRowToUser)
            .one()
            .doOnSuccess(user -> logger.debug("Found user by ID: {}", id))
            .doOnError(error -> logger.error("Failed to find user by ID: {}", id, error));
    }
    
    @Override
    public Mono<User> findByUsername(Username username) {
        String sql = "SELECT * FROM users WHERE username = $1";
        
        return databaseClient.sql(sql)
            .bind("$1", username.getValue())
            .map(this::mapRowToUser)
            .one()
            .doOnSuccess(user -> logger.debug("Found user by username: {}", username))
            .doOnError(error -> logger.error("Failed to find user by username: {}", username, error));
    }
    
    @Override
    public Mono<User> findByEmail(Email email) {
        String sql = "SELECT * FROM users WHERE email = $1";
        
        return databaseClient.sql(sql)
            .bind("$1", email.getValue())
            .map(this::mapRowToUser)
            .one()
            .doOnSuccess(user -> logger.debug("Found user by email: {}", email))
            .doOnError(error -> logger.error("Failed to find user by email: {}", email, error));
    }
    
    @Override
    public Mono<Boolean> existsByUsername(Username username) {
        String sql = "SELECT COUNT(*) FROM users WHERE username = $1";
        
        return databaseClient.sql(sql)
            .bind("$1", username.getValue())
            .map(row -> row.get(0, Long.class))
            .one()
            .map(count -> count > 0);
    }
    
    @Override
    public Mono<Boolean> existsByEmail(Email email) {
        String sql = "SELECT COUNT(*) FROM users WHERE email = $1";
        
        return databaseClient.sql(sql)
            .bind("$1", email.getValue())
            .map(row -> row.get(0, Long.class))
            .one()
            .map(count -> count > 0);
    }
    
    @Override
    public Flux<User> findAllActive() {
        String sql = "SELECT * FROM users WHERE is_active = true ORDER BY created_at DESC";
        
        return databaseClient.sql(sql)
            .map(this::mapRowToUser)
            .all();
    }
    
    @Override
    public Flux<User> findByRole(User.UserRole role) {
        String sql = "SELECT * FROM users WHERE role = $1 ORDER BY created_at DESC";
        
        return databaseClient.sql(sql)
            .bind("$1", role.name())
            .map(this::mapRowToUser)
            .all();
    }
    
    @Override
    public Mono<Void> deleteById(Id id) {
        String sql = "DELETE FROM users WHERE id = $1";
        
        return databaseClient.sql(sql)
            .bind("$1", Long.parseLong(id.getValue()))
            .then()
            .doOnSuccess(unused -> logger.debug("Deleted user with ID: {}", id))
            .doOnError(error -> logger.error("Failed to delete user with ID: {}", id, error));
    }
    
    @Override
    public Mono<Long> count() {
        String sql = "SELECT COUNT(*) FROM users";
        
        return databaseClient.sql(sql)
            .map(row -> row.get(0, Long.class))
            .one();
    }
    
    @Override
    public Mono<Long> countActive() {
        String sql = "SELECT COUNT(*) FROM users WHERE is_active = true";
        
        return databaseClient.sql(sql)
            .map(row -> row.get(0, Long.class))
            .one();
    }
    
    private User mapRowToUser(io.r2dbc.spi.Row row, io.r2dbc.spi.RowMetadata metadata) {
        User user = User.create(
            Username.of(row.get("username", String.class)),
            Email.of(row.get("email", String.class)),
            row.get("password_hash", String.class)
        );
        
        user.setId(Id.of(row.get("id", Long.class)));
        user.setAvatarUrl(row.get("avatar_url", String.class));
        user.setBio(row.get("bio", String.class));
        user.setGithubUsername(row.get("github_username", String.class));
        user.setWebsiteUrl(row.get("website_url", String.class));
        user.setRole(User.UserRole.valueOf(row.get("role", String.class)));
        user.setActive(row.get("is_active", Boolean.class));
        user.setCreatedAt(row.get("created_at", LocalDateTime.class));
        user.setUpdatedAt(row.get("updated_at", LocalDateTime.class));
        
        return user;
    }
} 