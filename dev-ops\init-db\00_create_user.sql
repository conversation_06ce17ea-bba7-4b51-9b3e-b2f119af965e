-- 创建数据库用户和授权

-- 创建用户
CREATE USER chatbot_user WITH PASSWORD 'chatbot_pass';

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE chatbot_db TO chatbot_user;

-- 确保用户可以创建表和操作数据
ALTER USER chatbot_user CREATEDB;

\c chatbot_db;

-- 在chatbot_db数据库中授予schema权限
GRANT ALL ON SCHEMA public TO chatbot_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO chatbot_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO chatbot_user;

-- 设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO chatbot_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO chatbot_user; 