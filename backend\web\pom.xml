<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.superwu</groupId>
        <artifactId>superwu</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>web</artifactId>
    <packaging>jar</packaging>
    <name>Web Layer</name>
    <description>接口层 - 包含REST控制器、DTO、异常处理、主启动类</description>

    <dependencies>
        <!-- 依赖应用层 -->
        <dependency>
            <groupId>com.superwu</groupId>
            <artifactId>application</artifactId>
        </dependency>
        
        <!-- 依赖基础设施层 -->
        <dependency>
            <groupId>com.superwu</groupId>
            <artifactId>infrastructure</artifactId>
        </dependency>
        
        <!-- Spring Boot Web Starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        
        <!-- Spring Boot Actuator (监控) -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        
        <!-- Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        
        <!-- Flyway Database Migration -->
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- Spring Boot Maven Plugin -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.superwu.SuperWuApplication</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project> 