#!/usr/bin/env python3
"""
RAG (Retrieval-Augmented Generation) 核心处理模块
包含文档处理、向量化、检索增强等功能
"""

import os
import hashlib
import pickle
import logging
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime
import asyncio
from pathlib import Path

# 文档处理
import PyPDF2
import docx
import markdown
import chardet
import jieba

# 向量化和检索
import numpy as np
from sentence_transformers import SentenceTransformer
import faiss

# 数据库
from sqlalchemy.orm import Session
from database import DocumentChunk, Document, KnowledgeRepository, SearchHistory

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DocumentProcessor:
    """文档处理器：负责文件解析和文本提取"""
    
    @staticmethod
    def calculate_file_hash(file_path: str) -> str:
        """计算文件SHA256哈希值"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
        return sha256_hash.hexdigest()
    
    @staticmethod
    def detect_encoding(file_path: str) -> str:
        """检测文件编码"""
        with open(file_path, 'rb') as f:
            raw_data = f.read(10000)  # 读取前10KB检测编码
            result = chardet.detect(raw_data)
            return result['encoding'] or 'utf-8'
    
    @staticmethod
    def extract_text_from_pdf(file_path: str) -> Tuple[str, Dict[str, Any]]:
        """从PDF文件提取文本"""
        try:
            text_content = ""
            metadata = {"pages": 0, "title": "", "author": ""}
            
            with open(file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                metadata["pages"] = len(reader.pages)
                
                # 提取元数据
                if reader.metadata:
                    metadata["title"] = reader.metadata.get('/Title', '')
                    metadata["author"] = reader.metadata.get('/Author', '')
                
                # 提取文本
                for page_num, page in enumerate(reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text_content += f"\n[第{page_num + 1}页]\n{page_text}\n"
                    except Exception as e:
                        logger.warning(f"提取第{page_num + 1}页失败: {e}")
                        continue
            
            return text_content.strip(), metadata
            
        except Exception as e:
            logger.error(f"PDF文件解析失败 {file_path}: {e}")
            raise Exception(f"PDF文件解析失败: {str(e)}")
    
    @staticmethod
    def extract_text_from_docx(file_path: str) -> Tuple[str, Dict[str, Any]]:
        """从DOCX文件提取文本"""
        try:
            doc = docx.Document(file_path)
            text_content = ""
            metadata = {"paragraphs": 0, "title": "", "author": ""}
            
            # 提取核心属性
            if hasattr(doc, 'core_properties'):
                metadata["title"] = doc.core_properties.title or ""
                metadata["author"] = doc.core_properties.author or ""
            
            # 提取段落文本
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content += paragraph.text + "\n"
                    metadata["paragraphs"] += 1
            
            return text_content.strip(), metadata
            
        except Exception as e:
            logger.error(f"DOCX文件解析失败 {file_path}: {e}")
            raise Exception(f"DOCX文件解析失败: {str(e)}")
    
    @staticmethod
    def extract_text_from_txt(file_path: str) -> Tuple[str, Dict[str, Any]]:
        """从TXT文件提取文本"""
        try:
            encoding = DocumentProcessor.detect_encoding(file_path)
            
            with open(file_path, 'r', encoding=encoding) as file:
                content = file.read()
            
            metadata = {
                "encoding": encoding,
                "lines": len(content.split('\n')),
                "characters": len(content)
            }
            
            return content, metadata
            
        except Exception as e:
            logger.error(f"TXT文件解析失败 {file_path}: {e}")
            raise Exception(f"TXT文件解析失败: {str(e)}")
    
    @staticmethod
    def extract_text_from_markdown(file_path: str) -> Tuple[str, Dict[str, Any]]:
        """从Markdown文件提取文本"""
        try:
            encoding = DocumentProcessor.detect_encoding(file_path)
            
            with open(file_path, 'r', encoding=encoding) as file:
                md_content = file.read()
            
            # 转换为HTML然后提取纯文本
            html = markdown.markdown(md_content)
            # 这里可以进一步处理HTML，提取纯文本
            # 暂时直接返回markdown内容
            
            metadata = {
                "encoding": encoding,
                "markdown_length": len(md_content),
                "has_headers": "# " in md_content
            }
            
            return md_content, metadata
            
        except Exception as e:
            logger.error(f"Markdown文件解析失败 {file_path}: {e}")
            raise Exception(f"Markdown文件解析失败: {str(e)}")
    
    @staticmethod
    def extract_text(file_path: str, file_type: str) -> Tuple[str, Dict[str, Any]]:
        """根据文件类型提取文本"""
        file_type = file_type.lower()
        
        if file_type == 'pdf':
            return DocumentProcessor.extract_text_from_pdf(file_path)
        elif file_type in ['docx', 'doc']:
            return DocumentProcessor.extract_text_from_docx(file_path)
        elif file_type == 'txt':
            return DocumentProcessor.extract_text_from_txt(file_path)
        elif file_type in ['md', 'markdown']:
            return DocumentProcessor.extract_text_from_markdown(file_path)
        else:
            raise Exception(f"不支持的文件类型: {file_type}")


class TextChunker:
    """文本分块器：将长文本分割成适合向量化的块"""
    
    @staticmethod
    def split_by_sentences(text: str, max_chunk_size: int = 1000, overlap: int = 200) -> List[Dict[str, Any]]:
        """基于句子的智能分块"""
        # 使用jieba进行中文分句
        sentences = []
        current_sentence = ""
        
        for char in text:
            current_sentence += char
            if char in ['。', '！', '？', '.', '!', '?', '\n']:
                if current_sentence.strip():
                    sentences.append(current_sentence.strip())
                current_sentence = ""
        
        if current_sentence.strip():
            sentences.append(current_sentence.strip())
        
        chunks = []
        current_chunk = ""
        start_char = 0
        
        for i, sentence in enumerate(sentences):
            # 如果当前块加上新句子超过最大长度，创建新块
            if len(current_chunk) + len(sentence) > max_chunk_size and current_chunk:
                chunk_info = {
                    "content": current_chunk.strip(),
                    "start_char": start_char,
                    "end_char": start_char + len(current_chunk),
                    "sentence_count": current_chunk.count('。') + current_chunk.count('！') + current_chunk.count('？')
                }
                chunks.append(chunk_info)
                
                # 处理重叠
                overlap_text = current_chunk[-overlap:] if len(current_chunk) > overlap else current_chunk
                start_char += len(current_chunk) - len(overlap_text)
                current_chunk = overlap_text + " " + sentence
            else:
                if not current_chunk:
                    start_char = sum(len(s) for s in sentences[:i])
                current_chunk += " " + sentence if current_chunk else sentence
        
        # 添加最后一个块
        if current_chunk.strip():
            chunk_info = {
                "content": current_chunk.strip(),
                "start_char": start_char,
                "end_char": start_char + len(current_chunk),
                "sentence_count": current_chunk.count('。') + current_chunk.count('！') + current_chunk.count('？')
            }
            chunks.append(chunk_info)
        
        return chunks
    
    @staticmethod
    def split_by_paragraphs(text: str, max_chunk_size: int = 1000, overlap: int = 200) -> List[Dict[str, Any]]:
        """基于段落的分块"""
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
        
        chunks = []
        current_chunk = ""
        start_char = 0
        
        for i, paragraph in enumerate(paragraphs):
            if len(current_chunk) + len(paragraph) > max_chunk_size and current_chunk:
                chunk_info = {
                    "content": current_chunk.strip(),
                    "start_char": start_char,
                    "end_char": start_char + len(current_chunk),
                    "paragraph_count": current_chunk.count('\n\n') + 1
                }
                chunks.append(chunk_info)
                
                # 处理重叠
                overlap_text = current_chunk[-overlap:] if len(current_chunk) > overlap else current_chunk
                start_char += len(current_chunk) - len(overlap_text)
                current_chunk = overlap_text + "\n\n" + paragraph
            else:
                if not current_chunk:
                    start_char = sum(len(p) + 2 for p in paragraphs[:i])  # +2 for \n\n
                current_chunk += "\n\n" + paragraph if current_chunk else paragraph
        
        if current_chunk.strip():
            chunk_info = {
                "content": current_chunk.strip(),
                "start_char": start_char,
                "end_char": start_char + len(current_chunk),
                "paragraph_count": current_chunk.count('\n\n') + 1
            }
            chunks.append(chunk_info)
        
        return chunks


class EmbeddingManager:
    """嵌入向量管理器：负责文本向量化和相似度检索"""
    
    def __init__(self, model_name: str = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"):
        self.model_name = model_name
        self.model = None
        self.faiss_index = None
        self.chunk_mappings = {}  # chunk_id -> faiss_index 映射
    
    def load_model(self):
        """加载嵌入模型"""
        if self.model is None:
            logger.info(f"加载嵌入模型: {self.model_name}")
            self.model = SentenceTransformer(self.model_name)
            logger.info("嵌入模型加载完成")
    
    def encode_texts(self, texts: List[str]) -> np.ndarray:
        """将文本列表编码为向量"""
        self.load_model()
        return self.model.encode(texts, convert_to_numpy=True)
    
    def encode_single_text(self, text: str) -> np.ndarray:
        """编码单个文本"""
        return self.encode_texts([text])[0]
    
    def create_faiss_index(self, embeddings: np.ndarray) -> faiss.IndexFlatIP:
        """创建FAISS索引用于快速相似度检索"""
        dimension = embeddings.shape[1]
        index = faiss.IndexFlatIP(dimension)  # 使用内积(余弦相似度)
        
        # 归一化向量以使用余弦相似度
        faiss.normalize_L2(embeddings)
        index.add(embeddings)
        
        return index
    
    def search_similar_chunks(
        self, 
        query_embedding: np.ndarray, 
        index: faiss.IndexFlatIP, 
        top_k: int = 5,
        threshold: float = 0.7
    ) -> Tuple[List[float], List[int]]:
        """在FAISS索引中搜索相似的文档块"""
        # 归一化查询向量
        query_embedding = query_embedding.reshape(1, -1)
        faiss.normalize_L2(query_embedding)
        
        # 搜索最相似的向量
        scores, indices = index.search(query_embedding, top_k)
        
        # 过滤低于阈值的结果
        filtered_scores = []
        filtered_indices = []
        
        for score, idx in zip(scores[0], indices[0]):
            if score >= threshold:
                filtered_scores.append(float(score))
                filtered_indices.append(int(idx))
        
        return filtered_scores, filtered_indices


class RAGProcessor:
    """RAG处理器：整合文档处理、向量化和检索功能"""
    
    def __init__(self, upload_dir: str = "uploads"):
        self.upload_dir = Path(upload_dir)
        self.upload_dir.mkdir(exist_ok=True)
        
        self.doc_processor = DocumentProcessor()
        self.text_chunker = TextChunker()
        self.embedding_manager = EmbeddingManager()
    
    async def upload_document(
        self, 
        file, 
        repository_id: str, 
        user_id: str, 
        db
    ) -> str:
        """上传并处理文档"""
        import tempfile
        import shutil
        
        # 获取DEFAULT_USER_ID
        DEFAULT_USER_ID = "550e8400-e29b-41d4-a716-446655440000"
        
        # 1. 保存上传的文件到临时目录
        temp_file = None
        try:
            # 创建临时文件
            suffix = Path(file.filename).suffix
            with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as temp_file:
                # 读取并保存文件内容
                content = await file.read()
                temp_file.write(content)
                temp_file_path = temp_file.name
            
            # 2. 处理文档
            document = await self.process_document(
                file_path=temp_file_path,
                repository_id=repository_id,
                original_filename=file.filename,
                user_id=user_id or DEFAULT_USER_ID,
                db=db
            )
            
            # 3. 将临时文件移动到正式存储位置
            final_path = self.upload_dir / repository_id / f"{document.id}{suffix}"
            final_path.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(temp_file_path, final_path)
            
            # 4. 更新文档的文件路径
            document.file_path = str(final_path)
            db.commit()
            
            return str(document.id)
            
        except Exception as e:
            # 清理临时文件
            if temp_file and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
            raise
    
    async def process_document(
        self, 
        file_path: str, 
        repository_id: str, 
        original_filename: str,
        user_id: str,
        db: Session
    ) -> Document:
        """完整的文档处理流程"""
        
        try:
            # 1. 计算文件哈希和基本信息
            file_hash = self.doc_processor.calculate_file_hash(file_path)
            file_size = os.path.getsize(file_path)
            file_type = Path(original_filename).suffix[1:].lower()
            
            # 2. 检查是否已存在相同文件
            existing_doc = db.query(Document).filter(
                Document.content_hash == file_hash,
                Document.repository_id == repository_id
            ).first()
            
            if existing_doc:
                raise Exception("相同内容的文档已存在于此仓库中")
            
            # 3. 创建文档记录
            document = Document(
                repository_id=repository_id,
                filename=Path(original_filename).stem,
                original_filename=original_filename,
                file_type=file_type,
                file_size=file_size,
                file_path=file_path,
                content_hash=file_hash,
                uploaded_by=user_id,
                processing_status="processing"
            )
            db.add(document)
            db.commit()
            db.refresh(document)
            
            # 4. 提取文本内容
            logger.info(f"开始处理文档: {original_filename}")
            text_content, metadata = self.doc_processor.extract_text(file_path, file_type)
            
            # 5. 更新文档元数据
            document.title = metadata.get('title') or Path(original_filename).stem
            document.author = metadata.get('author', '')
            document.doc_metadata = metadata
            
            # 6. 文本分块
            repository = db.query(KnowledgeRepository).filter(
                KnowledgeRepository.id == repository_id
            ).first()
            
            chunks = self.text_chunker.split_by_sentences(
                text_content,
                max_chunk_size=repository.chunk_size,
                overlap=repository.chunk_overlap
            )
            
            # 7. 向量化处理
            chunk_texts = [chunk["content"] for chunk in chunks]
            embeddings = self.embedding_manager.encode_texts(chunk_texts)
            
            # 8. 保存文档块
            for i, (chunk_info, embedding) in enumerate(zip(chunks, embeddings)):
                chunk = DocumentChunk(
                    document_id=document.id,
                    chunk_index=i,
                    content=chunk_info["content"],
                    content_length=len(chunk_info["content"]),
                    start_char=chunk_info["start_char"],
                    end_char=chunk_info["end_char"],
                    embedding_vector=pickle.dumps(embedding),
                    embedding_model=self.embedding_manager.model_name,
                    metadata=chunk_info
                )
                db.add(chunk)
            
            # 9. 更新统计信息
            document.chunk_count = len(chunks)
            document.processing_status = "completed"
            
            repository.document_count += 1
            repository.total_chunks += len(chunks)
            
            db.commit()
            
            logger.info(f"文档处理完成: {original_filename}, 生成 {len(chunks)} 个块")
            return document
            
        except Exception as e:
            # 处理失败，更新状态
            if 'document' in locals():
                document.processing_status = "failed"
                document.processing_error = str(e)
                db.commit()
            
            logger.error(f"文档处理失败: {str(e)}")
            raise
    
    async def search_knowledge(
        self,
        query: str,
        repository_id: str,
        top_k: int = 5,
        threshold: float = 0.7,
        user_id: str = None,
        db: Session = None
    ) -> List[Dict[str, Any]]:
        """在知识库中搜索相关内容"""
        
        start_time = datetime.now()
        
        try:
            # 1. 获取仓库中的所有文档块
            chunks = db.query(DocumentChunk).join(Document).filter(
                Document.repository_id == repository_id,
                Document.processing_status == "completed"
            ).all()
            
            if not chunks:
                return []
            
            # 2. 加载所有向量
            embeddings = []
            chunk_data = []
            
            for chunk in chunks:
                embedding = pickle.loads(chunk.embedding_vector)
                embeddings.append(embedding)
                chunk_data.append({
                    "chunk_id": str(chunk.id),
                    "document_id": str(chunk.document_id),
                    "content": chunk.content,
                    "chunk_index": chunk.chunk_index,
                    "metadata": chunk.chunk_metadata,
                    "document_filename": chunk.document.filename,
                    "document_title": chunk.document.title
                })
            
            # 3. 创建FAISS索引
            embeddings_array = np.array(embeddings)
            faiss_index = self.embedding_manager.create_faiss_index(embeddings_array)
            
            # 4. 向量化查询
            query_embedding = self.embedding_manager.encode_single_text(query)
            
            # 5. 执行检索
            scores, indices = self.embedding_manager.search_similar_chunks(
                query_embedding, faiss_index, top_k, threshold
            )
            
            # 6. 组装结果
            results = []
            for score, idx in zip(scores, indices):
                chunk_info = chunk_data[idx].copy()
                chunk_info["relevance_score"] = score
                results.append(chunk_info)
            
            # 7. 记录搜索历史
            if user_id and db:
                search_time = int((datetime.now() - start_time).total_seconds() * 1000)
                
                search_history = SearchHistory(
                    user_id=user_id,
                    repository_id=repository_id,
                    query=query,
                    results_count=len(results),
                    search_time_ms=search_time,
                    relevance_scores=scores,
                    retrieved_chunks=[chunk["chunk_id"] for chunk in results]
                )
                db.add(search_history)
                db.commit()
            
            logger.info(f"检索完成: 查询='{query}', 结果数={len(results)}")
            return results
            
        except Exception as e:
            logger.error(f"知识检索失败: {str(e)}")
            raise


# 全局RAG处理器实例
rag_processor = RAGProcessor() 