from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from datetime import datetime

# 基础模型
class MessageRequest(BaseModel):
    role: str
    content: str

class ModelParameters(BaseModel):
    temperature: float = 0.7
    max_tokens: int = 2000
    top_p: Optional[float] = 0.8

class ModelConfigRequest(BaseModel):
    id: Optional[str] = None
    name: str
    provider: str
    apiEndpoint: str
    apiKey: str
    model: str
    enabled: bool = True
    parameters: ModelParameters

class ChatRequest(BaseModel):
    modelId: str
    messages: List[MessageRequest]
    stream: bool = True
    conversationId: Optional[str] = None
    systemPrompt: Optional[str] = None
    title: Optional[str] = None

class ChatResponse(BaseModel):
    content: str
    model: str
    usage: Optional[Dict[str, Any]] = None

# RAG相关数据模型
class KnowledgeRepositoryRequest(BaseModel):
    name: str
    description: Optional[str] = None
    is_public: bool = False
    chunk_size: int = 1000
    chunk_overlap: int = 200

class KnowledgeRepositoryResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    is_public: bool
    document_count: int
    total_chunks: int
    embedding_model: str
    chunk_size: int
    chunk_overlap: int
    created_at: datetime
    updated_at: datetime

class DocumentResponse(BaseModel):
    id: str
    filename: str
    original_filename: str
    file_type: str
    file_size: int
    title: Optional[str]
    author: Optional[str]
    chunk_count: int
    processing_status: str
    processing_error: Optional[str]
    created_at: datetime

class SearchRequest(BaseModel):
    query: str
    repository_id: str
    top_k: int = 5
    threshold: float = 0.7

class SearchResult(BaseModel):
    chunk_id: str
    document_id: str
    content: str
    relevance_score: float
    document_title: str
    document_filename: str
    chunk_index: int

class RAGChatRequest(BaseModel):
    modelId: str
    messages: List[MessageRequest]
    repositoryId: str
    stream: bool = True
    conversationId: Optional[str] = None
    systemPrompt: Optional[str] = None
    title: Optional[str] = None
    ragTopK: int = 5
    ragThreshold: float = 0.7 