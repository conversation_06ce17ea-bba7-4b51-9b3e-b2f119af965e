package com.superwu.web.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;

/**
 * Redis连接测试控制器
 */
@RestController
@RequestMapping("/api/redis")
public class RedisTestController {
    
    @Autowired(required = false)
    private ReactiveRedisTemplate<String, Object> reactiveRedisTemplate;
    
    @GetMapping("/test")
    public Mono<Map<String, Object>> testRedis() {
        if (reactiveRedisTemplate == null) {
            Map<String, Object> errorResponse = Map.of(
                "status", "ERROR",
                "message", "Redis template not available",
                "timestamp", Instant.now()
            );
            return Mono.just(errorResponse);
        }

        String testKey = "test:connection:" + System.currentTimeMillis();
        String testValue = "Hello Redis!";

        return reactiveRedisTemplate.opsForValue()
            .set(testKey, testValue, Duration.ofMinutes(1))
            .then(reactiveRedisTemplate.opsForValue().get(testKey))
            .map(value -> {
                Map<String, Object> successResponse = Map.of(
                    "status", "SUCCESS",
                    "message", "Redis connection successful",
                    "testKey", testKey,
                    "testValue", value != null ? value : "null",
                    "timestamp", Instant.now()
                );
                return successResponse;
            })
            .onErrorReturn(Map.of(
                "status", "ERROR",
                "message", "Redis connection failed",
                "timestamp", Instant.now()
            ));
    }
    
    @GetMapping("/ping")
    public Mono<Map<String, Object>> pingRedis() {
        if (reactiveRedisTemplate == null) {
            Map<String, Object> errorResponse = Map.of(
                "status", "ERROR",
                "message", "Redis template not available"
            );
            return Mono.just(errorResponse);
        }

        return reactiveRedisTemplate.getConnectionFactory()
            .getReactiveConnection()
            .ping()
            .map(response -> {
                Map<String, Object> successResponse = Map.of(
                    "status", "SUCCESS",
                    "ping", response,
                    "timestamp", Instant.now()
                );
                return successResponse;
            })
            .onErrorReturn(Map.of(
                "status", "ERROR",
                "message", "Redis ping failed",
                "timestamp", Instant.now()
            ));
    }
}
