#!/usr/bin/env python3
"""
检查和更新数据库中的模型配置
"""

import sqlite3
import json

def check_and_update_database():
    """检查和更新数据库中的模型配置"""
    try:
        # 连接数据库
        conn = sqlite3.connect('chat_app.db')
        cursor = conn.cursor()
        
        print("🔍 查询当前模型配置...")
        
        # 查询所有模型配置
        cursor.execute("SELECT id, name, provider, api_endpoint, model FROM model_configs")
        models = cursor.fetchall()
        
        print(f"📊 找到 {len(models)} 个模型配置:")
        for model in models:
            print(f"  - ID: {model[0]}")
            print(f"    名称: {model[1]}")
            print(f"    提供商: {model[2]}")
            print(f"    API端点: {model[3]}")
            print(f"    模型: {model[4]}")
            print()
        
        # 查找DashScope模型
        cursor.execute("""
            SELECT id, name, provider, api_endpoint, model 
            FROM model_configs 
            WHERE provider = 'dashscope' AND model = 'qwen-vl-plus'
        """)
        dashscope_model = cursor.fetchone()
        
        if dashscope_model:
            print(f"✅ 找到DashScope模型:")
            print(f"  - ID: {dashscope_model[0]}")
            print(f"  - 名称: {dashscope_model[1]}")
            print(f"  - 当前API端点: {dashscope_model[3]}")
            
            # 检查是否需要更新
            current_endpoint = dashscope_model[3]
            correct_endpoint = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
            
            if current_endpoint != correct_endpoint:
                print(f"🔄 需要更新API端点...")
                print(f"  从: {current_endpoint}")
                print(f"  到: {correct_endpoint}")
                
                # 更新API端点
                cursor.execute("""
                    UPDATE model_configs 
                    SET api_endpoint = ? 
                    WHERE id = ?
                """, (correct_endpoint, dashscope_model[0]))
                
                conn.commit()
                print("✅ API端点更新成功！")
                
                # 验证更新
                cursor.execute("""
                    SELECT api_endpoint 
                    FROM model_configs 
                    WHERE id = ?
                """, (dashscope_model[0],))
                updated_endpoint = cursor.fetchone()[0]
                print(f"🔍 验证更新后的端点: {updated_endpoint}")
                
            else:
                print("✅ API端点已经是正确的，无需更新")
        else:
            print("❌ 未找到DashScope模型配置")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        if 'conn' in locals():
            conn.close()

def show_table_structure():
    """显示表结构"""
    try:
        conn = sqlite3.connect('chat_app.db')
        cursor = conn.cursor()
        
        print("📋 model_configs表结构:")
        cursor.execute("PRAGMA table_info(model_configs)")
        columns = cursor.fetchall()
        
        for column in columns:
            print(f"  - {column[1]} ({column[2]})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 查看表结构失败: {e}")

if __name__ == "__main__":
    print("🔧 开始检查和更新数据库...")
    show_table_structure()
    print()
    check_and_update_database()
    print("🎯 操作完成！")
