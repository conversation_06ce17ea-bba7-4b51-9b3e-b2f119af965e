#!/usr/bin/env python3
"""
多模型聊天机器人 Python 后端启动脚本
"""

import uvicorn
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

if __name__ == "__main__":
    port = int(os.getenv("PORT", 3002))  # 改为3002端口
    
    print("🐍 启动Python后端服务器...")
    print(f"📍 服务地址: http://localhost:{port}")
    print("🚀 API文档: http://localhost:{}/docs".format(port))
    print("📋 Redoc文档: http://localhost:{}/redoc".format(port))
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=port,
        reload=True,  # 开发模式自动重载
        log_level="info"
    ) 