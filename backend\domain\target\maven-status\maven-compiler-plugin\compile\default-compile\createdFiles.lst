com\superwu\domain\user\entity\User.class
com\superwu\domain\user\repository\UserRepository.class
com\superwu\domain\user\valueobject\Email.class
com\superwu\domain\user\entity\User$UserRole.class
com\superwu\domain\shared\valueobject\Id.class
com\superwu\domain\shared\entity\BaseEntity.class
com\superwu\domain\shared\exception\DomainException.class
com\superwu\domain\shared\valueobject\Id$IdType.class
com\superwu\domain\user\valueobject\Username.class
