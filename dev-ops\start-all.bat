@echo off
chcp 65001
echo 🚀 启动多模型聊天机器人 (Docker版本)
echo ==================================

REM 检查Docker
docker --version > nul 2>&1
if errorlevel 1 (
    echo ❌ 请先安装Docker Desktop
    pause
    exit /b 1
)

REM 检查Docker Compose
docker-compose --version > nul 2>&1
if errorlevel 1 (
    docker compose version > nul 2>&1
    if errorlevel 1 (
        echo ❌ 请先安装Docker Compose
        pause
        exit /b 1
    )
)

REM 进入dev-ops目录
cd /d "%~dp0"

echo 📦 构建并启动所有服务...
docker-compose up --build -d

echo.
echo ⏳ 等待服务启动...
timeout /t 10 /nobreak > nul

echo.
echo 📊 检查服务状态...
docker-compose ps

echo.
echo 🎉 服务启动完成！
echo.
echo 📍 访问地址：
echo    前端应用:      http://localhost:3000
echo    后端API:       http://localhost:3002
echo    API文档:       http://localhost:3002/docs
echo    数据库管理:    http://localhost:8080 (pgAdmin)
echo    PostgreSQL:    localhost:5432
echo    Redis:         localhost:6379
echo.
echo 🔑 数据库连接信息：
echo    数据库: chatbot_db
echo    用户名: chatbot_user
echo    密码: chatbot_pass
echo.
echo 🔧 pgAdmin登录信息：
echo    邮箱: <EMAIL>
echo    密码: admin123
echo.
echo 📝 查看日志: docker-compose logs -f [服务名]
echo 🛑 停止服务: docker-compose down
echo 🗑️  清理数据: docker-compose down -v
echo.
pause 