from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from sqlalchemy import func
from typing import List, AsyncGenerator, Any
import json
from datetime import datetime

from database import get_db
from database import (
    KnowledgeRepository as DBKnowledgeRepository, 
    Document as DBDocument, 
    DocumentChunk as DBDocumentChunk,
    SearchHistory as DBSearchHistory,
    Conversation as DBConversation
)
from rag_core import RAGProcessor
from .models import (
    KnowledgeRepositoryRequest, KnowledgeRepositoryResponse,
    DocumentResponse, SearchRequest, SearchResult, RAGChatRequest
)
from .utils import DEFAULT_USER_ID, save_message, create_conversation_with_rag
from .chat_handlers import stream_chat_response
from chat_context import ContextManager, ChatMessage
from database import User as DBUser, ModelConfig as DBModelConfig

router = APIRouter()

# 知识仓库管理API
@router.get("/api/knowledge/repositories", response_model=List[KnowledgeRepositoryResponse])
async def get_repositories(db: Session = Depends(get_db)):
    """获取知识仓库列表"""
    repositories = db.query(DBKnowledgeRepository).filter(
        DBKnowledgeRepository.owner_id == DEFAULT_USER_ID
    ).all()
    
    result = []
    for repo in repositories:
        # 统计文档和块数量
        doc_count = db.query(func.count(DBDocument.id)).filter(
            DBDocument.repository_id == repo.id
        ).scalar()
        
        chunk_count = db.query(func.count(DBDocumentChunk.id)).join(
            DBDocument, DBDocumentChunk.document_id == DBDocument.id
        ).filter(DBDocument.repository_id == repo.id).scalar()
        
        result.append(KnowledgeRepositoryResponse(
            id=str(repo.id),
            name=repo.name,
            description=repo.description,
            is_public=repo.is_public,
            document_count=doc_count or 0,
            total_chunks=chunk_count or 0,
            embedding_model=repo.embedding_model,
            chunk_size=repo.chunk_size,
            chunk_overlap=repo.chunk_overlap,
            created_at=repo.created_at,
            updated_at=repo.updated_at
        ))
    
    return result

@router.post("/api/knowledge/repositories")
async def create_repository(request: KnowledgeRepositoryRequest, db: Session = Depends(get_db)):
    """创建新的知识仓库"""
    # 检查名称是否已存在
    existing = db.query(DBKnowledgeRepository).filter(
        DBKnowledgeRepository.name == request.name,
        DBKnowledgeRepository.owner_id == DEFAULT_USER_ID
    ).first()
    
    if existing:
        raise HTTPException(status_code=400, detail="知识仓库名称已存在")
    
    # 创建新仓库
    repository = DBKnowledgeRepository(
        name=request.name,
        description=request.description,
        owner_id=DEFAULT_USER_ID,
        is_public=request.is_public,
        chunk_size=request.chunk_size,
        chunk_overlap=request.chunk_overlap,
        embedding_model="sentence-transformers/all-MiniLM-L6-v2"  # 默认模型
    )
    
    db.add(repository)
    db.commit()
    db.refresh(repository)
    
    return {"success": True, "id": str(repository.id)}

@router.get("/api/knowledge/repositories/{repository_id}")
async def get_repository(repository_id: str, db: Session = Depends(get_db)):
    """获取知识仓库详情"""
    repository = db.query(DBKnowledgeRepository).filter(
        DBKnowledgeRepository.id == repository_id
    ).first()
    
    if not repository:
        raise HTTPException(status_code=404, detail="知识仓库未找到")
    
    # 统计信息
    doc_count = db.query(func.count(DBDocument.id)).filter(
        DBDocument.repository_id == repository.id
    ).scalar()
    
    chunk_count = db.query(func.count(DBDocumentChunk.id)).join(
        DBDocument, DBDocumentChunk.document_id == DBDocument.id
    ).filter(DBDocument.repository_id == repository.id).scalar()
    
    return KnowledgeRepositoryResponse(
        id=str(repository.id),
        name=repository.name,
        description=repository.description,
        is_public=repository.is_public,
        document_count=doc_count or 0,
        total_chunks=chunk_count or 0,
        embedding_model=repository.embedding_model,
        chunk_size=repository.chunk_size,
        chunk_overlap=repository.chunk_overlap,
        created_at=repository.created_at,
        updated_at=repository.updated_at
    )

@router.put("/api/knowledge/repositories/{repository_id}")
async def update_repository(
    repository_id: str, 
    request: KnowledgeRepositoryRequest, 
    db: Session = Depends(get_db)
):
    """更新知识仓库"""
    repository = db.query(DBKnowledgeRepository).filter(
        DBKnowledgeRepository.id == repository_id
    ).first()
    
    if not repository:
        raise HTTPException(status_code=404, detail="知识仓库未找到")
    
    # 检查名称冲突（除了当前仓库）
    existing = db.query(DBKnowledgeRepository).filter(
        DBKnowledgeRepository.name == request.name,
        DBKnowledgeRepository.owner_id == DEFAULT_USER_ID,
        DBKnowledgeRepository.id != repository_id
    ).first()
    
    if existing:
        raise HTTPException(status_code=400, detail="知识仓库名称已存在")
    
    # 更新字段
    repository.name = request.name
    repository.description = request.description
    repository.is_public = request.is_public
    repository.chunk_size = request.chunk_size
    repository.chunk_overlap = request.chunk_overlap
    repository.updated_at = datetime.now()
    
    db.commit()
    return {"success": True}

@router.delete("/api/knowledge/repositories/{repository_id}")
async def delete_repository(repository_id: str, db: Session = Depends(get_db)):
    """删除知识仓库"""
    repository = db.query(DBKnowledgeRepository).filter(
        DBKnowledgeRepository.id == repository_id
    ).first()
    
    if not repository:
        raise HTTPException(status_code=404, detail="知识仓库未找到")
    
    # 删除相关的所有数据（级联删除）
    db.delete(repository)
    db.commit()
    
    return {"success": True}

# 文档管理API
@router.post("/api/knowledge/repositories/{repository_id}/upload")
async def upload_document(
    repository_id: str,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """上传文档到知识仓库"""
    # 检查仓库是否存在
    repository = db.query(DBKnowledgeRepository).filter(
        DBKnowledgeRepository.id == repository_id
    ).first()
    
    if not repository:
        raise HTTPException(status_code=404, detail="知识仓库未找到")
    
    try:
        import os
        import tempfile
        import shutil
        from pathlib import Path
        import hashlib
        
        # 计算文件哈希
        content = await file.read()
        file_hash = hashlib.sha256(content).hexdigest()
        
        # 重置文件指针
        await file.seek(0)
        
        # 检查是否已存在相同文件
        existing_doc = db.query(DBDocument).filter(
            DBDocument.content_hash == file_hash,
            DBDocument.repository_id == repository_id
        ).first()
        
        if existing_doc:
            raise HTTPException(status_code=400, detail="相同内容的文档已存在于此仓库中")
        
        # 创建上传目录
        upload_dir = Path("uploads") / repository_id
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存文件
        file_extension = Path(file.filename).suffix
        file_stem = Path(file.filename).stem
        final_filename = f"{file_hash[:8]}_{file_stem}{file_extension}"
        final_path = upload_dir / final_filename
        
        with open(final_path, "wb") as f:
            f.write(content)
        
        # 创建文档记录
        document = DBDocument(
            repository_id=repository_id,
            filename=file_stem,
            original_filename=file.filename,
            file_type=file_extension[1:].lower() if file_extension else "unknown",
            file_size=len(content),
            file_path=str(final_path),
            content_hash=file_hash,
            uploaded_by=DEFAULT_USER_ID,
            processing_status="completed",  # 暂时标记为完成
            title=file_stem
        )
        db.add(document)
        
        # 更新仓库统计
        repository.document_count += 1
        
        db.commit()
        db.refresh(document)
        
        return {"success": True, "document_id": str(document.id)}
        
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"文档上传失败: {str(e)}")

@router.get("/api/knowledge/repositories/{repository_id}/documents", response_model=List[DocumentResponse])
async def get_repository_documents(repository_id: str, db: Session = Depends(get_db)):
    """获取知识仓库的文档列表"""
    documents = db.query(DBDocument).filter(
        DBDocument.repository_id == repository_id
    ).order_by(DBDocument.created_at.desc()).all()
    
    result = []
    for doc in documents:
        # 统计块数量
        chunk_count = db.query(func.count(DBDocumentChunk.id)).filter(
            DBDocumentChunk.document_id == doc.id
        ).scalar()
        
        result.append(DocumentResponse(
            id=str(doc.id),
            filename=doc.filename,
            original_filename=doc.original_filename,
            file_type=doc.file_type,
            file_size=doc.file_size,
            title=doc.title,
            author=doc.author,
            chunk_count=chunk_count or 0,
            processing_status=doc.processing_status,
            processing_error=doc.processing_error,
            created_at=doc.created_at
        ))
    
    return result

@router.get("/api/knowledge/documents/{document_id}")
async def get_document(document_id: str, db: Session = Depends(get_db)):
    """获取文档详情"""
    document = db.query(DBDocument).filter(DBDocument.id == document_id).first()
    
    if not document:
        raise HTTPException(status_code=404, detail="文档未找到")
    
    # 统计块数量
    chunk_count = db.query(func.count(DBDocumentChunk.id)).filter(
        DBDocumentChunk.document_id == document.id
    ).scalar()
    
    return DocumentResponse(
        id=str(document.id),
        filename=document.filename,
        original_filename=document.original_filename,
        file_type=document.file_type,
        file_size=document.file_size,
        title=document.title,
        author=document.author,
        chunk_count=chunk_count or 0,
        processing_status=document.processing_status,
        processing_error=document.processing_error,
        created_at=document.created_at
    )

@router.post("/api/knowledge/documents/{document_id}/reprocess")
async def reprocess_document(
    document_id: str,
    db: Session = Depends(get_db)
):
    """重新处理文档"""
    try:
        # 查找文档
        document = db.query(DBDocument).filter(DBDocument.id == document_id).first()

        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")

        # 检查文件是否存在
        if not document.file_path or not os.path.exists(document.file_path):
            raise HTTPException(status_code=400, detail="文档文件不存在")

        # 获取文件类型
        file_path = Path(document.file_path)
        file_type = file_path.suffix.lower().lstrip('.')

        # 重新处理文档
        logger.info(f"开始重新处理文档: {document.filename}")

        # 提取文本内容
        text_content, metadata = rag_processor.doc_processor.extract_text(str(file_path), file_type)

        if not text_content.strip():
            raise HTTPException(status_code=400, detail="文档内容为空")

        # 更新文档元数据
        document.title = metadata.get('title') or Path(document.filename).stem
        document.author = metadata.get('author', '')
        document.doc_metadata = metadata

        # 获取知识库配置
        repository = db.query(DBKnowledgeRepository).filter(
            DBKnowledgeRepository.id == document.repository_id
        ).first()

        # 文本分块
        chunks = rag_processor.text_chunker.split_by_sentences(
            text_content,
            max_chunk_size=repository.chunk_size,
            overlap=repository.chunk_overlap
        )

        if not chunks:
            raise HTTPException(status_code=400, detail="文本分块失败")

        # 向量化处理
        chunk_texts = [chunk["content"] for chunk in chunks]
        embeddings = rag_processor.embedding_manager.encode_texts(chunk_texts)

        # 删除旧的文档块
        db.query(DBDocumentChunk).filter(DBDocumentChunk.document_id == document_id).delete()

        # 保存新的文档块
        import pickle
        for i, (chunk_info, embedding) in enumerate(zip(chunks, embeddings)):
            chunk = DBDocumentChunk(
                document_id=document.id,
                chunk_index=i,
                content=chunk_info["content"],
                content_length=len(chunk_info["content"]),
                start_char=chunk_info["start_char"],
                end_char=chunk_info["end_char"],
                embedding_vector=pickle.dumps(embedding),
                embedding_model=rag_processor.embedding_manager.model_name,
                metadata=chunk_info
            )
            db.add(chunk)

        # 更新文档统计信息
        document.chunk_count = len(chunks)
        document.processing_status = "completed"
        document.processing_error = None

        # 更新知识库统计信息
        repository.total_chunks = db.query(DBDocumentChunk).join(DBDocument).filter(
            DBDocument.repository_id == repository.id
        ).count()

        db.commit()

        logger.info(f"文档重新处理完成: {document.filename}, 生成 {len(chunks)} 个块")

        return {
            "success": True,
            "message": "文档重新处理成功",
            "chunk_count": len(chunks)
        }

    except Exception as e:
        logger.error(f"重新处理文档失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"重新处理文档失败: {str(e)}")

@router.delete("/api/knowledge/documents/{document_id}")
async def delete_document(document_id: str, db: Session = Depends(get_db)):
    """删除文档"""
    document = db.query(DBDocument).filter(DBDocument.id == document_id).first()
    
    if not document:
        raise HTTPException(status_code=404, detail="文档未找到")
    
    # 删除文档（级联删除块）
    db.delete(document)
    db.commit()
    
    return {"success": True}

# 知识检索API
@router.post("/api/knowledge/search", response_model=List[SearchResult])
async def search_knowledge(request: SearchRequest, db: Session = Depends(get_db)):
    """在知识仓库中搜索相关内容"""
    try:
        # 检查仓库是否存在
        repository = db.query(DBKnowledgeRepository).filter(
            DBKnowledgeRepository.id == request.repository_id
        ).first()
        
        if not repository:
            raise HTTPException(status_code=404, detail="知识仓库未找到")
        
        # 使用RAG处理器搜索
        from rag_core import rag_processor
        results = await rag_processor.search_knowledge(
            query=request.query,
            repository_id=request.repository_id,
            top_k=request.top_k,
            threshold=request.threshold,
            user_id=DEFAULT_USER_ID,
            db=db
        )
        
        # 记录搜索历史
        search_history = DBSearchHistory(
            user_id=DEFAULT_USER_ID,
            repository_id=request.repository_id,
            query=request.query,
            result_count=len(results)
        )
        db.add(search_history)
        db.commit()
        
        # 转换为API响应格式
        search_results = []
        for result in results:
            search_results.append(SearchResult(
                chunk_id=str(result["chunk_id"]),
                document_id=str(result["document_id"]),
                content=result["content"],
                relevance_score=result["relevance_score"],
                document_title=result["document_title"],
                document_filename=result["document_filename"],
                chunk_index=result["chunk_index"]
            ))
        
        return search_results
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

# RAG增强对话API
@router.post("/api/rag/chat")
async def rag_chat(request: RAGChatRequest, db: Session = Depends(get_db)):
    """RAG增强的聊天对话"""
    conversation = None
    
    try:
        # 兼容旧的字符串ID
        model_id = request.modelId
        if model_id == "qwen":
            model_id = "550e8400-e29b-41d4-a716-446655440001"
        
        # 查找模型配置
        model_config = db.query(DBModelConfig).filter(DBModelConfig.id == model_id).first()
        if not model_config:
            raise HTTPException(status_code=404, detail="模型未找到")
        
        if not model_config.enabled:
            raise HTTPException(status_code=400, detail="模型已禁用")
        
        # 检查知识仓库
        repository = db.query(DBKnowledgeRepository).filter(
            DBKnowledgeRepository.id == request.repositoryId
        ).first()
        
        if not repository:
            raise HTTPException(status_code=404, detail="知识仓库未找到")
        
        # 获取或创建对话（启用RAG）
        if request.conversationId:
            try:
                import uuid
                uuid.UUID(request.conversationId)
                conversation = db.query(DBConversation).filter(
                    DBConversation.id == request.conversationId
                ).first()
            except (ValueError, Exception):
                conversation = None
        
        if not conversation:
            # 创建启用RAG的新对话
            user = db.query(DBUser).filter(DBUser.id == DEFAULT_USER_ID).first()
            if not user:
                user = DBUser(
                    id=DEFAULT_USER_ID,
                    username="默认用户",
                    email="<EMAIL>"
                )
                db.add(user)
                db.flush()
            
            from .utils import create_conversation_with_rag
            conversation = create_conversation_with_rag(
                user_id=user.id,
                model_id=str(model_config.id),
                title=request.title or "RAG对话",
                db=db,
                rag_enabled=True,
                rag_repository_id=request.repositoryId,
                rag_top_k=request.ragTopK,
                rag_threshold=request.ragThreshold,
                system_prompt=request.systemPrompt
            )
        
        # 使用RAG处理器进行知识检索
        user_message = request.messages[-1]
        from rag_core import rag_processor
        
        # 搜索相关知识
        search_results = await rag_processor.search_knowledge(
            query=user_message.content,
            repository_id=request.repositoryId,
            top_k=request.ragTopK,
            threshold=request.ragThreshold,
            user_id=DEFAULT_USER_ID,
            db=db
        )
        
        # 构建RAG增强的提示
        if search_results:
            context_content = "\n\n".join([
                f"[文档: {result['document_title']}]\n{result['content']}"
                for result in search_results
            ])
            
            enhanced_prompt = f"""基于以下知识库内容回答用户问题：

{context_content}

用户问题: {user_message.content}

请基于上述知识库内容给出准确的回答。如果知识库中没有相关信息，请明确说明。"""
        else:
            enhanced_prompt = f"""用户问题: {user_message.content}

知识库中没有找到相关信息，请基于你的通用知识回答，并说明这不是基于特定知识库的回答。"""
        
        # 获取上下文管理器
        from chat_context import ContextManager, ChatMessage
        context = await ContextManager.get_or_create_context(str(conversation.id))
        
        # 添加增强后的用户消息到上下文
        await context.add_message(ChatMessage(
            role="user",
            content=enhanced_prompt,
            token_count=len(enhanced_prompt) // 4
        ))
        
        # 获取API调用的消息列表
        api_messages = await context.get_messages_for_api(conversation.system_prompt)
        
        # 保存原始用户消息到数据库
        from datetime import datetime
        user_metadata = {
            "timestamp": datetime.now().isoformat(),
            "rag_enabled": True,
            "repository_id": request.repositoryId,
            "search_results_count": len(search_results),
            "rag_enhanced": True
        }
        
        from .utils import save_message
        user_msg = save_message(
            conversation_id=str(conversation.id),
            role="user",
            content=user_message.content,  # 保存原始用户消息
            model_id=str(model_config.id),
            msg_metadata=user_metadata,
            db=db
        )
        
        # 返回流式或非流式响应
        if request.stream:
            # 传递ID而不是对象，避免会话分离问题
            return StreamingResponse(
                stream_rag_chat_response(str(model_config.id), api_messages, context, str(conversation.id)),
                media_type="text/plain",
                headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
            )
        else:
            # 非流式响应处理
            raise HTTPException(status_code=501, detail="RAG非流式响应暂未实现")
        
    except Exception as e:
        print(f"RAG聊天处理错误: {e}")
        if conversation:
            db.rollback()
        raise HTTPException(status_code=500, detail=f"RAG聊天处理失败: {str(e)}")

async def stream_rag_chat_response(
    model_config_id: str,
    messages: List[dict],
    context: Any,
    conversation_id: str
) -> AsyncGenerator[str, None]:
    """RAG流式聊天响应"""
    from database import SessionLocal
    
    # 创建新的数据库会话
    db = SessionLocal()
    try:
        # 先发送对话ID
        yield f"data: {json.dumps({'conversationId': conversation_id}, ensure_ascii=False)}\n\n"
        
        # 重新查询模型配置
        model_config = db.query(DBModelConfig).filter(DBModelConfig.id == model_config_id).first()
        if not model_config:
            yield f"data: {json.dumps({'error': '模型配置未找到'}, ensure_ascii=False)}\n\n"
            return
        
        # 收集完整响应内容
        full_response = ""
        
        # 构建模型配置字典
        config_dict = {
            "id": str(model_config.id),
            "name": model_config.name,
            "provider": model_config.provider,
            "apiEndpoint": model_config.api_endpoint,
            "apiKey": model_config.api_key_encrypted,
            "model": model_config.model,
            "parameters": model_config.parameters
        }
        
        # 流式生成回复
        async for chunk in stream_chat_response(config_dict, messages):
            if chunk:
                full_response += chunk
                yield f"data: {json.dumps({'content': chunk}, ensure_ascii=False)}\n\n"
        
        # 添加助手消息到上下文
        assistant_message = ChatMessage(
            role="assistant",
            content=full_response,
            token_count=len(full_response) // 4
        )
        await context.add_message(assistant_message)
        
        # 保存助手消息到数据库
        from datetime import datetime
        assistant_metadata = {
            "timestamp": datetime.now().isoformat(),
            "response_length": len(full_response),
            "model_used": model_config.model,
            "rag_enabled": True
        }
        
        from .utils import save_message
        assistant_msg = save_message(
            conversation_id=conversation_id,
            role="assistant",
            content=full_response,
            model_id=str(model_config.id),
            msg_metadata=assistant_metadata,
            db=db
        )
        
        yield f"data: [DONE]\n\n"
        
    except Exception as e:
        yield f"data: {json.dumps({'error': str(e)}, ensure_ascii=False)}\n\n"
    finally:
        db.close()

 