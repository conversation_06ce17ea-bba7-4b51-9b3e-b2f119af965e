# 开发环境配置 - 使用默认postgres用户
spring:
  # R2DBC 数据库配置
  r2dbc:
    url: r2dbc:postgresql://localhost:5432/postgres
    username: postgres
    password: postgres
    pool:
      initial-size: 5
      max-size: 10
      max-idle-time: 30m
      validation-query: SELECT 1
  
  # Flyway 数据库迁移配置
  flyway:
    url: *****************************************
    user: postgres
    password: postgres
    locations: classpath:db/migration
    baseline-on-migrate: true
  
  # Redis 配置
  data:
    redis:
      host: localhost
      port: 6379
      timeout: 3000ms
      database: 0

# 日志配置
logging:
  level:
    com.superwu: DEBUG
    org.springframework.r2dbc: DEBUG
    io.r2dbc.postgresql: DEBUG 