#!/usr/bin/env python3
"""
更新数据库中的DashScope模型配置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db_session, ModelConfig

def update_dashscope_endpoint():
    """更新DashScope模型的API端点"""
    try:
        # 获取数据库会话
        session = next(get_db_session())
        
        # 查找DashScope模型
        dashscope_model = session.query(ModelConfig).filter(
            ModelConfig.provider == 'dashscope',
            ModelConfig.model == 'qwen-vl-plus'
        ).first()
        
        if dashscope_model:
            print(f'✅ 找到DashScope模型: {dashscope_model.name}')
            print(f'📍 当前API端点: {dashscope_model.api_endpoint}')
            
            # 更新API端点
            new_endpoint = 'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions'
            dashscope_model.api_endpoint = new_endpoint
            session.commit()
            
            print(f'🔄 已更新API端点: {new_endpoint}')
            print('✅ 数据库更新成功！')
        else:
            print('❌ 未找到DashScope模型')
            
        session.close()
        
    except Exception as e:
        print(f'❌ 更新失败: {e}')
        if 'session' in locals():
            session.rollback()
            session.close()

if __name__ == "__main__":
    print("🔧 开始更新DashScope模型配置...")
    update_dashscope_endpoint()
    print("🎯 更新完成！")
