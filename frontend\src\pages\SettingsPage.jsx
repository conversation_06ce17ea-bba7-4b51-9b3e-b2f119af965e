import React, { useState, useEffect } from 'react'
import { Save, RefreshCw, Download, Upload, Trash2 } from 'lucide-react'
import useChatStore from '../store/chatStore'

const SettingsPage = () => {
  const { conversations, models } = useChatStore()
  const [settings, setSettings] = useState({
    theme: 'light',
    autoSave: true,
    maxConversations: 50,
    defaultModel: '',
    enableNotifications: true,
    language: 'zh-CN'
  })

  useEffect(() => {
    // 从localStorage加载设置
    const savedSettings = localStorage.getItem('app-settings')
    if (savedSettings) {
      setSettings(JSON.parse(savedSettings))
    }
  }, [])

  const saveSettings = () => {
    localStorage.setItem('app-settings', JSON.stringify(settings))
    alert('设置已保存')
  }

  const exportData = () => {
    const data = {
      conversations,
      models: models.map(m => ({ ...m, apiKey: '***' })), // 不导出API密钥
      settings,
      exportTime: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `chatbot-backup-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const importData = (event) => {
    const file = event.target.files[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target.result)
        
        if (data.conversations) {
          localStorage.setItem('chat-storage', JSON.stringify({
            state: {
              conversations: data.conversations,
              currentConversationId: null,
              currentModel: null
            }
          }))
        }
        
        if (data.settings) {
          setSettings(data.settings)
          localStorage.setItem('app-settings', JSON.stringify(data.settings))
        }
        
        alert('数据导入成功！请刷新页面以查看更改。')
      } catch (error) {
        alert('导入失败：文件格式不正确')
      }
    }
    reader.readAsText(file)
  }

  const clearAllData = () => {
    if (confirm('确定要清除所有数据吗？这将删除所有对话历史和设置，此操作不可恢复。')) {
      localStorage.removeItem('chat-storage')
      localStorage.removeItem('app-settings')
      alert('所有数据已清除，请刷新页面。')
    }
  }

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStorageInfo = () => {
    const chatData = localStorage.getItem('chat-storage')
    const settingsData = localStorage.getItem('app-settings')
    const totalSize = (chatData?.length || 0) + (settingsData?.length || 0)
    return {
      conversations: conversations.length,
      models: models.length,
      storageSize: formatFileSize(totalSize)
    }
  }

  const storageInfo = getStorageInfo()

  return (
    <div className="flex-1 p-6">
      <div className="max-w-4xl mx-auto">
        {/* 页面标题 */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">设置</h1>
          <p className="text-gray-600 mt-1">管理应用设置和数据</p>
        </div>

        <div className="space-y-6">
          {/* 基本设置 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">基本设置</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  主题
                </label>
                <select
                  value={settings.theme}
                  onChange={(e) => setSettings(prev => ({ ...prev, theme: e.target.value }))}
                  className="input-field"
                >
                  <option value="light">浅色</option>
                  <option value="dark">深色</option>
                  <option value="system">跟随系统</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  语言
                </label>
                <select
                  value={settings.language}
                  onChange={(e) => setSettings(prev => ({ ...prev, language: e.target.value }))}
                  className="input-field"
                >
                  <option value="zh-CN">简体中文</option>
                  <option value="en-US">English</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  默认模型
                </label>
                <select
                  value={settings.defaultModel}
                  onChange={(e) => setSettings(prev => ({ ...prev, defaultModel: e.target.value }))}
                  className="input-field"
                >
                  <option value="">自动选择</option>
                  {models.filter(m => m.enabled).map(model => (
                    <option key={model.id} value={model.id}>{model.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  最大对话数量
                </label>
                <input
                  type="number"
                  min="10"
                  max="1000"
                  value={settings.maxConversations}
                  onChange={(e) => setSettings(prev => ({ ...prev, maxConversations: parseInt(e.target.value) }))}
                  className="input-field"
                />
                <p className="text-xs text-gray-500 mt-1">超过此数量时会自动删除最旧的对话</p>
              </div>
            </div>

            <div className="mt-6 space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="autoSave"
                  checked={settings.autoSave}
                  onChange={(e) => setSettings(prev => ({ ...prev, autoSave: e.target.checked }))}
                  className="mr-3"
                />
                <label htmlFor="autoSave" className="text-sm font-medium text-gray-700">
                  自动保存对话
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="notifications"
                  checked={settings.enableNotifications}
                  onChange={(e) => setSettings(prev => ({ ...prev, enableNotifications: e.target.checked }))}
                  className="mr-3"
                />
                <label htmlFor="notifications" className="text-sm font-medium text-gray-700">
                  启用通知
                </label>
              </div>
            </div>

            <div className="mt-6">
              <button onClick={saveSettings} className="btn-primary flex items-center space-x-2">
                <Save className="h-4 w-4" />
                <span>保存设置</span>
              </button>
            </div>
          </div>

          {/* 数据管理 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">数据管理</h2>
            
            {/* 存储信息 */}
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <h3 className="text-sm font-medium text-gray-700 mb-3">存储信息</h3>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">{storageInfo.conversations}</div>
                  <div className="text-sm text-gray-500">对话数量</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">{storageInfo.models}</div>
                  <div className="text-sm text-gray-500">配置模型</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">{storageInfo.storageSize}</div>
                  <div className="text-sm text-gray-500">存储大小</div>
                </div>
              </div>
            </div>

            {/* 数据操作 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={exportData}
                className="btn-secondary flex items-center justify-center space-x-2"
              >
                <Download className="h-4 w-4" />
                <span>导出数据</span>
              </button>

              <label className="btn-secondary flex items-center justify-center space-x-2 cursor-pointer">
                <Upload className="h-4 w-4" />
                <span>导入数据</span>
                <input
                  type="file"
                  accept=".json"
                  onChange={importData}
                  className="hidden"
                />
              </label>

              <button
                onClick={clearAllData}
                className="btn-danger flex items-center justify-center space-x-2"
              >
                <Trash2 className="h-4 w-4" />
                <span>清除所有数据</span>
              </button>
            </div>

            <div className="mt-4 text-sm text-gray-500">
              <p>• 导出数据不包含API密钥信息</p>
              <p>• 导入数据后需要重新配置API密钥</p>
              <p>• 清除数据操作不可恢复，请谨慎操作</p>
            </div>
          </div>

          {/* 关于信息 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">关于</h2>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">应用名称:</span>
                <span className="font-medium">多模型聊天机器人</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">版本:</span>
                <span className="font-medium">v1.0.0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">构建时间:</span>
                <span className="font-medium">{new Date().toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">技术栈:</span>
                <span className="font-medium">React + Vite + Node.js</span>
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="text-sm font-medium text-blue-900 mb-2">功能特性</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• 支持多种AI模型（通义千问、OpenAI、Claude等）</li>
                <li>• 实时聊天对话，支持Markdown格式</li>
                <li>• 对话历史管理和导出</li>
                <li>• 模型参数自定义配置</li>
                <li>• 响应式设计，支持多种设备</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SettingsPage 