from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import os
import asyncio

# 导入数据库初始化
from database import create_tables

# 导入API路由
from api.models_api import router as models_router
from api.conversations_api import router as conversations_router  
from api.chat_api import router as chat_router
from api.rag_api import router as rag_router

# 导入工具函数
from api.utils import init_default_data

# 创建FastAPI应用
app = FastAPI(
    title="SuperWu多模型聊天机器人",
    description="支持多种AI模型的聊天机器人API",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000", "file://", "*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(models_router)
app.include_router(conversations_router)
app.include_router(chat_router)
app.include_router(rag_router)

# 挂载静态文件
uploads_dir = "uploads"
if not os.path.exists(uploads_dir):
    os.makedirs(uploads_dir)

app.mount("/uploads", StaticFiles(directory=uploads_dir), name="uploads")

# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    print("🚀 启动多模型聊天机器人服务...")
    
    # 初始化数据库
    create_tables()
    print("✅ 数据库初始化完成")
    
    # 初始化默认数据
    init_default_data()
    
    print("✅ 服务启动完成！")
    print("📖 API文档: http://localhost:3002/docs")
    print("🔗 交互式文档: http://localhost:3002/redoc")

# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理"""
    print("👋 关闭多模型聊天机器人服务...")

# 根路径
@app.get("/")
async def root():
    """根路径接口"""
    return {
        "message": "SuperWu多模型聊天机器人API",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }

# 健康检查
@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": "chat-bot-api"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=3002,
        reload=True,
        log_level="info"
    ) 