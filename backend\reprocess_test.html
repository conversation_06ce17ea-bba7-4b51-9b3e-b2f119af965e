<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档重新处理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: white;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
    </style>
</head>
<body>
    <h1>文档重新处理测试工具</h1>
    
    <div class="container">
        <h3>步骤1: 获取文档列表</h3>
        <button onclick="getDocuments()">获取文档列表</button>
        <div id="documents-result"></div>
    </div>
    
    <div class="container">
        <h3>步骤2: 重新处理文档</h3>
        <input type="text" id="document-id" placeholder="输入文档ID" style="width: 300px; padding: 8px;">
        <button onclick="reprocessDocument()">重新处理文档</button>
        <div id="reprocess-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3002';
        const REPO_ID = 'd356ecae-6d96-4b0c-a7dd-51db9d81c16a';

        async function getDocuments() {
            const resultDiv = document.getElementById('documents-result');
            resultDiv.innerHTML = '<p>正在获取文档列表...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/api/knowledge/repositories/${REPO_ID}/documents`);
                const data = await response.json();
                
                if (response.ok) {
                    let html = '<div class="result success"><h4>文档列表:</h4>';
                    data.forEach(doc => {
                        html += `
                            <div style="margin: 10px 0; padding: 10px; background: white; border-radius: 4px;">
                                <strong>${doc.filename}</strong><br>
                                ID: ${doc.id}<br>
                                分块数: ${doc.chunk_count}<br>
                                状态: ${doc.processing_status}<br>
                                ${doc.processing_error ? `错误: ${doc.processing_error}<br>` : ''}
                                <button onclick="document.getElementById('document-id').value='${doc.id}'">选择此文档</button>
                            </div>
                        `;
                    });
                    html += '</div>';
                    resultDiv.innerHTML = html;
                } else {
                    resultDiv.innerHTML = `<div class="result error">获取文档列表失败: ${data.detail || '未知错误'}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">请求失败: ${error.message}</div>`;
            }
        }

        async function reprocessDocument() {
            const documentId = document.getElementById('document-id').value.trim();
            const resultDiv = document.getElementById('reprocess-result');
            
            if (!documentId) {
                resultDiv.innerHTML = '<div class="result error">请输入文档ID</div>';
                return;
            }
            
            resultDiv.innerHTML = '<p>正在重新处理文档...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/api/knowledge/documents/${documentId}/reprocess`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h4>重新处理成功!</h4>
                            <p>消息: ${data.message}</p>
                            <p>生成块数: ${data.chunk_count}</p>
                        </div>
                    `;
                    // 自动刷新文档列表
                    setTimeout(getDocuments, 1000);
                } else {
                    resultDiv.innerHTML = `<div class="result error">重新处理失败: ${data.detail || '未知错误'}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">请求失败: ${error.message}</div>`;
            }
        }

        // 页面加载时自动获取文档列表
        window.onload = function() {
            getDocuments();
        };
    </script>
</body>
</html>
