# 测试知识库搜索功能

Write-Host "🔍 测试知识库搜索功能..." -ForegroundColor Green

$queries = @("WL-210", "分散剂", "使用方法", "技术参数")
$repoId = "d356ecae-6d96-4b0c-a7dd-51db9d81c16a"

foreach ($query in $queries) {
    Write-Host "`n📋 搜索: '$query'" -ForegroundColor Yellow
    
    try {
        $body = @{
            query = $query
            repository_id = $repoId
            top_k = 3
            threshold = 0.5
        } | ConvertTo-Json
        
        $response = Invoke-RestMethod -Uri "http://localhost:3002/api/knowledge/search" -Method POST -Body $body -ContentType "application/json"
        
        if ($response.Count -gt 0) {
            Write-Host "  找到 $($response.Count) 个相关结果:" -ForegroundColor Green
            for ($i = 0; $i -lt $response.Count; $i++) {
                $result = $response[$i]
                Write-Host "    $($i+1). 相关度: $([math]::Round($result.relevance_score, 3))" -ForegroundColor Cyan
                Write-Host "       文档: $($result.document_title)" -ForegroundColor White
                Write-Host "       内容: $($result.content.Substring(0, [Math]::Min(100, $result.content.Length)))..." -ForegroundColor Gray
            }
        } else {
            Write-Host "  ❌ 没有找到相关结果" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "  ❌ 搜索失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n🎉 搜索测试完成!" -ForegroundColor Green
