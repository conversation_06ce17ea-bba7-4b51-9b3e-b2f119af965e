package com.superwu.domain.user.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.io.Serializable;
import java.util.regex.Pattern;

/**
 * 用户名值对象
 * 确保用户名格式的有效性
 */
@Getter
@EqualsAndHashCode
public class Username implements Serializable {
    
    // 用户名规则：3-50个字符，只能包含字母、数字、下划线和连字符
    private static final Pattern USERNAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_-]{3,50}$");
    
    private final String value;
    
    private Username(String value) {
        this.value = value;
    }
    
    /**
     * 创建用户名值对象
     */
    public static Username of(String username) {
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("Username cannot be null or empty");
        }
        
        String trimmedUsername = username.trim();
        if (!USERNAME_PATTERN.matcher(trimmedUsername).matches()) {
            throw new IllegalArgumentException(
                "Username must be 3-50 characters long and contain only letters, numbers, underscores, and hyphens"
            );
        }
        
        return new Username(trimmedUsername);
    }
    
    /**
     * 检查用户名长度
     */
    public int length() {
        return value.length();
    }
    
    /**
     * 检查是否包含特殊字符
     */
    public boolean hasSpecialChars() {
        return value.contains("_") || value.contains("-");
    }
    
    @Override
    public String toString() {
        return value;
    }
} 