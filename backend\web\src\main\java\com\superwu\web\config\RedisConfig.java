package com.superwu.web.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.ReactiveRedisConnectionFactory;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * Redis配置类
 * 配置响应式Redis连接和序列化
 */
@Configuration
public class RedisConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(RedisConfig.class);

    /**
     * 配置响应式Redis模板
     * 只有在Redis配置存在时才创建
     */
    @Bean
    @ConditionalOnProperty(name = "spring.data.redis.host")
    public ReactiveRedisTemplate<String, Object> reactiveRedisTemplate(
            ReactiveRedisConnectionFactory connectionFactory) {
        
        logger.info("Configuring ReactiveRedisTemplate...");
        
        try {
            // 配置序列化器
            StringRedisSerializer stringSerializer = new StringRedisSerializer();
            GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer();
            
            // 构建序列化上下文
            RedisSerializationContext<String, Object> serializationContext = 
                RedisSerializationContext.<String, Object>newSerializationContext()
                    .key(stringSerializer)
                    .hashKey(stringSerializer)
                    .value(jsonSerializer)
                    .hashValue(jsonSerializer)
                    .build();
            
            ReactiveRedisTemplate<String, Object> template = new ReactiveRedisTemplate<>(connectionFactory, serializationContext);
            logger.info("ReactiveRedisTemplate configured successfully");
            return template;
        } catch (Exception e) {
            logger.error("Failed to configure ReactiveRedisTemplate: {}", e.getMessage(), e);
            throw e;
        }
    }
}
