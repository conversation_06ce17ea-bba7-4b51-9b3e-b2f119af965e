package com.superwu.domain.user.entity;

import com.superwu.domain.shared.entity.BaseEntity;
import com.superwu.domain.shared.valueobject.Id;
import com.superwu.domain.user.valueobject.Email;
import com.superwu.domain.user.valueobject.Username;
import lombok.Getter;
import lombok.Setter;

/**
 * 用户实体
 * 用户认证和基本信息
 */
@Getter
@Setter
public class User extends BaseEntity {
    
    private Username username;
    private Email email;
    private String passwordHash;
    private String avatarUrl;
    private String bio;
    private String githubUsername;
    private String websiteUrl;
    private UserRole role;
    private boolean isActive;
    
    protected User() {
        super();
        this.role = UserRole.USER;
        this.isActive = true;
    }
    
    protected User(Id id) {
        super(id);
        this.role = UserRole.USER;
        this.isActive = true;
    }
    
    /**
     * 创建新用户
     */
    public static User create(Username username, Email email, String passwordHash) {
        User user = new User();
        user.username = username;
        user.email = email;
        user.passwordHash = passwordHash;
        return user;
    }
    
    /**
     * 更新用户资料
     */
    public void updateProfile(String bio, String githubUsername, String websiteUrl) {
        this.bio = bio;
        this.githubUsername = githubUsername;
        this.websiteUrl = websiteUrl;
        touch();
    }
    
    /**
     * 更新头像
     */
    public void updateAvatar(String avatarUrl) {
        this.avatarUrl = avatarUrl;
        touch();
    }
    
    /**
     * 更改密码
     */
    public void changePassword(String newPasswordHash) {
        this.passwordHash = newPasswordHash;
        touch();
    }
    
    /**
     * 激活用户
     */
    public void activate() {
        this.isActive = true;
        touch();
    }
    
    /**
     * 停用用户
     */
    public void deactivate() {
        this.isActive = false;
        touch();
    }
    
    /**
     * 检查是否为管理员
     */
    public boolean isAdmin() {
        return role == UserRole.ADMIN;
    }
    
    /**
     * 用户角色枚举
     */
    public enum UserRole {
        USER("普通用户"),
        ADMIN("管理员");
        
        private final String description;
        
        UserRole(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
} 