# 🐳 多模型聊天机器人 - Docker部署指南

## 🚀 快速开始

### 环境要求
- Docker 20.0+
- Docker Compose 2.0+
- 8GB+ 可用内存
- 网络连接

### 一键启动

#### Windows
```bash
dev-ops/start-all.bat
```

#### Linux/Mac
```bash
chmod +x dev-ops/start-all.sh
./dev-ops/start-all.sh
```

## 📊 服务架构

```mermaid
graph TB
    U[用户] --> F[前端 Nginx:3000]
    F --> B[后端 FastAPI:3002]
    B --> P[PostgreSQL:5432]
    B --> R[Redis:6379]
    A[管理员] --> PG[pgAdmin:8080]
    PG --> P
```

## 🔧 服务说明

| 服务 | 端口 | 描述 | 访问地址 |
|------|------|------|----------|
| 前端 | 3000 | React应用 | http://localhost:3000 |
| 后端 | 3002 | FastAPI服务 | http://localhost:3002 |
| PostgreSQL | 5432 | 主数据库 | localhost:5432 |
| Redis | 6379 | 缓存/会话 | localhost:6379 |
| pgAdmin | 8080 | 数据库管理 | http://localhost:8080 |

## 📝 常用命令

### 启动服务
```bash
# 完整启动
./start-all.sh

# 仅启动数据库
docker-compose up postgres redis -d

# 重新构建启动
docker-compose up --build
```

### 查看状态
```bash
# 查看所有容器状态
docker-compose ps

# 查看服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f postgres
```

### 停止服务
```bash
# 停止所有服务
./stop-all.sh
# 或
docker-compose down

# 停止并删除数据
docker-compose down -v
```

### 清理环境
```bash
# 完整清理
./cleanup.sh

# 手动清理
docker-compose down -v
docker system prune -f
```

## 🔧 配置说明

### 环境变量
在 `docker-compose.yml` 中可以配置：

```yaml
environment:
  - DATABASE_URL=****************************************************/chatbot_db
  - REDIS_URL=redis://redis:6379
  - DASHSCOPE_API_KEY=your_api_key_here
  - PORT=3002
```

### 数据持久化
- PostgreSQL数据: `postgres_data` 卷
- Redis数据: `redis_data` 卷

### 网络配置
所有服务运行在 `chatbot-network` 网络中，支持服务间通信。

## 🛠️ 开发模式

### 本地开发
```bash
# 只启动数据库服务
docker-compose up postgres redis -d

# 本地运行后端
cd python-server
pip install -r requirements.txt
python start.py

# 本地运行前端
npm install
npm run dev
```

### 热重载
开发模式下，代码更改会自动重新加载：
- 后端: 通过 uvicorn 的 `--reload` 参数
- 前端: 通过 Vite 的热更新

## 📊 监控和调试

### 数据库管理
访问 http://localhost:8080 使用 pgAdmin：
- 邮箱: `<EMAIL>`
- 密码: `admin123`

添加服务器连接：
- 主机: `postgres`
- 端口: `5432`
- 数据库: `chatbot_db`
- 用户名: `chatbot_user`
- 密码: `chatbot_pass`

### API文档
访问 http://localhost:3002/docs 查看 OpenAPI 文档

### 日志查看
```bash
# 实时查看所有日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f postgres
```

## 🔒 生产部署

### 安全配置
1. 更改默认密码
2. 配置防火墙规则
3. 启用 HTTPS
4. 配置备份策略

### 性能优化
1. 调整 PostgreSQL 配置
2. 配置 Redis 持久化
3. 启用 Nginx 压缩
4. 配置 CDN

### 扩展部署
```yaml
# 多实例部署示例
backend:
  scale: 3  # 启动3个后端实例
  
# 使用外部数据库
postgres:
  external: true
  host: your-postgres-host
```

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :3000
   # 修改 docker-compose.yml 中的端口映射
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库容器状态
   docker-compose logs postgres
   # 重启数据库
   docker-compose restart postgres
   ```

3. **内存不足**
   ```bash
   # 检查资源使用
   docker stats
   # 增加 Docker 内存限制
   ```

### 重置环境
```bash
# 完全重置
./cleanup.sh
./start-all.sh
```

## 📚 更多信息

- [Python后端文档](../PYTHON_BACKEND.md)
- [前端开发指南](../README.md)
- [API接口文档](http://localhost:3002/docs)
- [数据库设计](../dev-ops/init-db/01_init.sql) 