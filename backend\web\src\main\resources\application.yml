server:
  port: 8080

spring:
  profiles:
    active: dev
  application:
    name: superwu-tech-platform
  
  # R2DBC 数据库配置
  r2dbc:
    url: r2dbc:postgresql://localhost:5432/superwu_db
    username: superwu
    password: superwu123
    pool:
      initial-size: 10
      max-size: 20
      max-idle-time: 30m
      validation-query: SELECT 1
  
  # Flyway 数据库迁移配置
  flyway:
    url: *******************************************
    user: superwu
    password: superwu123
    locations: classpath:db/migration
    baseline-on-migrate: true
  
  # Redis 配置
  data:
    redis:
      host: localhost
      port: 6379
      timeout: 3000ms
      database: 0
      connect-timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
        shutdown-timeout: 100ms
  
  # JSON 配置
  jackson:
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# 日志配置
logging:
  level:
    com.superwu: DEBUG
    org.springframework.r2dbc: DEBUG
    io.r2dbc.postgresql: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
  health:
    redis:
      enabled: false

---
spring:
  config:
    activate:
      on-profile: dev

logging:
  level:
    root: INFO
    com.superwu: DEBUG

---
spring:
  config:
    activate:
      on-profile: prod

logging:
  level:
    root: WARN
    com.superwu: INFO 