package com.superwu.application.user.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 用户响应DTO
 */
public record UserResponse(
    String id,
    String username,
    String email,
    String avatarUrl,
    String bio,
    String githubUsername,
    String websiteUrl,
    String role,
    boolean isActive,
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime createdAt,
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    LocalDateTime updatedAt
) {} 