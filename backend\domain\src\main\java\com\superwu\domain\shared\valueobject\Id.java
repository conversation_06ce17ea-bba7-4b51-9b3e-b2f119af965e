package com.superwu.domain.shared.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

/**
 * ID值对象
 * 支持Long类型和UUID类型的ID
 */
@Getter
@EqualsAndHashCode
public class Id implements Serializable {
    
    private final Long longValue;
    private final UUID uuidValue;
    private final IdType type;
    
    private Id(Long longValue) {
        this.longValue = Objects.requireNonNull(longValue, "Long ID cannot be null");
        this.uuidValue = null;
        this.type = IdType.LONG;
    }
    
    private Id(UUID uuidValue) {
        this.uuidValue = Objects.requireNonNull(uuidValue, "UUID ID cannot be null");
        this.longValue = null;
        this.type = IdType.UUID;
    }
    
    /**
     * 创建Long类型的ID
     */
    public static Id of(Long value) {
        return new Id(value);
    }
    
    /**
     * 创建UUID类型的ID
     */
    public static Id of(UUID value) {
        return new Id(value);
    }
    
    /**
     * 从字符串创建ID
     */
    public static Id of(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("ID string cannot be null or empty");
        }
        
        // 尝试解析为Long
        try {
            return new Id(Long.parseLong(value));
        } catch (NumberFormatException e) {
            // 尝试解析为UUID
            try {
                return new Id(UUID.fromString(value));
            } catch (IllegalArgumentException ex) {
                throw new IllegalArgumentException("Invalid ID format: " + value);
            }
        }
    }
    
    /**
     * 生成新的UUID类型ID
     */
    public static Id generate() {
        return new Id(UUID.randomUUID());
    }
    
    /**
     * 获取ID的字符串表示
     */
    public String getValue() {
        return type == IdType.LONG ? longValue.toString() : uuidValue.toString();
    }
    
    /**
     * 检查是否为Long类型
     */
    public boolean isLong() {
        return type == IdType.LONG;
    }
    
    /**
     * 检查是否为UUID类型
     */
    public boolean isUuid() {
        return type == IdType.UUID;
    }
    
    @Override
    public String toString() {
        return getValue();
    }
    
    /**
     * ID类型枚举
     */
    public enum IdType {
        LONG, UUID
    }
} 