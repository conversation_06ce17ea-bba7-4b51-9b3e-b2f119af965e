import React, { useEffect } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { MessageCircle, Settings, Cpu, Plus, Trash2, Database } from 'lucide-react'
import useChatStore from '../store/chatStore'

const Layout = ({ children }) => {
  const location = useLocation()
  const {
    conversations,
    currentConversationId,
    createConversation,
    setCurrentConversation,
    deleteConversation,
    initialize
  } = useChatStore()

  useEffect(() => {
    initialize()
  }, [initialize])

  const navigation = [
    { name: '聊天', href: '/', icon: MessageCircle },
    { name: '模型管理', href: '/models', icon: Cpu },
    { name: '知识库', href: '/knowledge', icon: Database },
    { name: '设置', href: '/settings', icon: Settings },
  ]

  const isActive = (path) => location.pathname === path

  return (
    <div className="flex h-screen bg-gray-50">
      {/* 侧边栏 */}
      <div className="w-64 bg-white shadow-sm flex flex-col">
        {/* 应用标题 */}
        <div className="p-4 border-b border-gray-200">
          <h1 className="text-xl font-bold text-gray-800">多模型聊天机器人</h1>
        </div>

        {/* 导航菜单 */}
        <nav className="p-4 border-b border-gray-200">
          <ul className="space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon
              return (
                <li key={item.name}>
                  <Link
                    to={item.href}
                    className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      isActive(item.href)
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                    }`}
                  >
                    <Icon className="mr-3 h-5 w-5" />
                    {item.name}
                  </Link>
                </li>
              )
            })}
          </ul>
        </nav>

        {/* 对话历史 */}
        {location.pathname === '/' && (
          <div className="flex-1 overflow-hidden flex flex-col">
            <div className="p-4 flex items-center justify-between">
              <h3 className="text-sm font-medium text-gray-700">对话历史</h3>
              <button
                onClick={createConversation}
                className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
                title="新建对话"
              >
                <Plus className="h-4 w-4" />
              </button>
            </div>
            
            <div className="flex-1 overflow-y-auto scrollbar-thin px-4 pb-4">
              {conversations.length === 0 ? (
                <p className="text-sm text-gray-500 text-center">暂无对话历史</p>
              ) : (
                <ul className="space-y-2">
                  {conversations.map((conversation) => (
                    <li key={conversation.id}>
                      <div
                        className={`group relative flex items-center justify-between p-2 rounded-md cursor-pointer transition-colors ${
                          currentConversationId === conversation.id
                            ? 'bg-blue-100 text-blue-700'
                            : 'text-gray-600 hover:bg-gray-100'
                        }`}
                        onClick={() => setCurrentConversation(conversation.id)}
                      >
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">
                            {conversation.title}
                          </p>
                          <p className="text-xs text-gray-500">
                            {new Date(conversation.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                        
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteConversation(conversation.id)
                          }}
                          className="opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-500 transition-all"
                          title="删除对话"
                        >
                          <Trash2 className="h-3 w-3" />
                        </button>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        )}
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {children}
      </div>
    </div>
  )
}

export default Layout 