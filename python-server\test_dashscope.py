#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DashScope API 测试脚本
测试通过OpenAI兼容接口调用阿里云DashScope服务
"""

import os
import json
from openai import OpenAI

def test_dashscope_basic():
    """测试基本的DashScope API调用"""
    print("🚀 开始测试DashScope API...")
    
    # 创建OpenAI客户端，使用DashScope兼容接口
    client = OpenAI(
        api_key="sk-4606dfde828a4f9aa7a43f5d53dddb9e",
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )
    
    try:
        print("📤 发送聊天请求...")
        completion = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "你是谁？请简单介绍一下自己。"},
            ],
            temperature=0.7,
            max_tokens=500
        )
        
        print("✅ API调用成功！")
        print(f"📝 模型: {completion.model}")
        print(f"💬 回复: {completion.choices[0].message.content}")
        print(f"🔢 使用tokens: {completion.usage.total_tokens}")
        
        return True
        
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        return False

def test_dashscope_stream():
    """测试流式响应"""
    print("\n🌊 开始测试流式响应...")
    
    client = OpenAI(
        api_key="sk-4606dfde828a4f9aa7a43f5d53dddb9e",
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )
    
    try:
        print("📤 发送流式聊天请求...")
        stream = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "请用1-2句话介绍一下Python编程语言。"},
            ],
            stream=True,
            temperature=0.7,
            max_tokens=200
        )
        
        print("📥 接收流式响应:")
        full_response = ""
        chunk_count = 0
        
        for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                chunk_count += 1
                content = chunk.choices[0].delta.content
                full_response += content
                print(f"Chunk {chunk_count}: {content}", end="", flush=True)
        
        print(f"\n✅ 流式响应完成！")
        print(f"📊 总共收到 {chunk_count} 个chunks")
        print(f"📝 完整回复: {full_response}")
        
        return True
        
    except Exception as e:
        print(f"❌ 流式响应失败: {e}")
        return False

def test_dashscope_raw_api():
    """测试原始DashScope API（非OpenAI兼容接口）"""
    print("\n🔧 开始测试原始DashScope API...")
    
    import httpx
    
    url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    headers = {
        "Authorization": "Bearer sk-4606dfde828a4f9aa7a43f5d53dddb9e",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "qwen-plus",
        "input": {
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "1+1等于几？"}
            ]
        },
        "parameters": {
            "temperature": 0.7,
            "max_tokens": 100
        }
    }
    
    try:
        print("📤 发送原始API请求...")
        with httpx.Client(timeout=30.0) as client:
            response = client.post(url, json=payload, headers=headers)
            
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 原始API调用成功！")
            print(f"📝 回复: {result['output']['choices'][0]['message']['content']}")
            return True
        else:
            print(f"❌ 原始API调用失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 原始API请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 DashScope API 测试套件")
    print("=" * 60)
    
    results = []
    
    # 测试1: 基本API调用
    results.append(test_dashscope_basic())
    
    # 测试2: 流式响应
    results.append(test_dashscope_stream())
    
    # 测试3: 原始API
    results.append(test_dashscope_raw_api())
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"✅ 成功: {sum(results)}/{len(results)}")
    print(f"❌ 失败: {len(results) - sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 所有测试通过！DashScope API工作正常。")
    else:
        print("⚠️  部分测试失败，请检查API配置。")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
