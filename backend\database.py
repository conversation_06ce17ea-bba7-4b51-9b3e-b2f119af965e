from sqlalchemy import create_engine, Column, String, DateTime, <PERSON>olean, Integer, Text, DECIMAL, ForeignKey, Index, Float, LargeBinary
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY
from sqlalchemy.sql import func
import uuid
import os
from datetime import datetime
import json
import gzip
import base64

# 数据库连接配置
# 临时切换到SQLite以避免PostgreSQL连接问题
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./chat_app.db")
# DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:postgres123@localhost:5432/chatbot_db")

engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# 用户模型
class User(Base):
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    username = Column(String(100), unique=True, nullable=False)
    email = Column(String(255), unique=True)
    avatar_url = Column(Text)
    preferences = Column(JSONB, default={})
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    conversations = relationship("Conversation", back_populates="user")
    model_configs = relationship("ModelConfig", back_populates="creator")
    chat_statistics = relationship("ChatStatistic", back_populates="user")
    knowledge_repositories = relationship("KnowledgeRepository", back_populates="owner")

# 模型配置
class ModelConfig(Base):
    __tablename__ = "model_configs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    provider = Column(String(50), nullable=False)
    api_endpoint = Column(Text, nullable=False)
    api_key_encrypted = Column(Text, nullable=False)
    model = Column(String(100), nullable=False)
    enabled = Column(Boolean, default=True)
    parameters = Column(JSONB, default={})
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    creator = relationship("User", back_populates="model_configs")
    conversations = relationship("Conversation", back_populates="model")
    chat_statistics = relationship("ChatStatistic", back_populates="model")

# 对话会话
class Conversation(Base):
    __tablename__ = "conversations"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    title = Column(String(200), nullable=False, default="新对话")
    model_id = Column(UUID(as_uuid=True), ForeignKey("model_configs.id"))
    rag_repository_id = Column(UUID(as_uuid=True), ForeignKey("knowledge_repositories.id"))
    system_prompt = Column(Text)
    is_archived = Column(Boolean, default=False)
    message_count = Column(Integer, default=0)
    rag_enabled = Column(Boolean, default=False)
    rag_top_k = Column(Integer, default=5)
    rag_threshold = Column(Float, default=0.7)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    user = relationship("User", back_populates="conversations")
    model = relationship("ModelConfig", back_populates="conversations")
    knowledge_repository = relationship("KnowledgeRepository", back_populates="conversations", foreign_keys=[rag_repository_id])
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
    chat_statistics = relationship("ChatStatistic", back_populates="conversation")
    
    # 索引
    __table_args__ = (
        Index('idx_conversations_user_id', 'user_id'),
        Index('idx_conversations_created_at', 'created_at'),
    )

# 消息
class Message(Base):
    __tablename__ = "messages"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("conversations.id", ondelete="CASCADE"))
    role = Column(String(20), nullable=False)  # user, assistant, system
    content = Column(Text, nullable=False)
    content_type = Column(String(20), default='text')  # text, image, mixed
    attachments = Column(JSONB, default=[])  # 存储图片等附件信息
    msg_metadata = Column(JSONB, default={})
    token_count = Column(Integer)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    conversation = relationship("Conversation", back_populates="messages")
    
    # 索引
    __table_args__ = (
        Index('idx_messages_conversation_id', 'conversation_id'),
        Index('idx_messages_created_at', 'created_at'),
    )

# 聊天统计
class ChatStatistic(Base):
    __tablename__ = "chat_statistics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    conversation_id = Column(UUID(as_uuid=True), ForeignKey("conversations.id"))
    model_id = Column(UUID(as_uuid=True), ForeignKey("model_configs.id"))
    request_tokens = Column(Integer, default=0)
    response_tokens = Column(Integer, default=0)
    total_cost = Column(DECIMAL(10, 6), default=0)
    response_time_ms = Column(Integer)
    status = Column(String(20), default="success")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    user = relationship("User", back_populates="chat_statistics")
    conversation = relationship("Conversation", back_populates="chat_statistics")
    model = relationship("ModelConfig", back_populates="chat_statistics")
    
    # 索引
    __table_args__ = (
        Index('idx_chat_statistics_user_id', 'user_id'),
        Index('idx_chat_statistics_created_at', 'created_at'),
    )

# 知识仓库
class KnowledgeRepository(Base):
    __tablename__ = "knowledge_repositories"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(200), nullable=False)
    description = Column(Text)
    owner_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    is_public = Column(Boolean, default=False)
    document_count = Column(Integer, default=0)
    total_chunks = Column(Integer, default=0)
    embedding_model = Column(String(100), default="sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2")
    chunk_size = Column(Integer, default=1000)
    chunk_overlap = Column(Integer, default=200)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    owner = relationship("User", back_populates="knowledge_repositories")
    documents = relationship("Document", back_populates="repository", cascade="all, delete-orphan")
    conversations = relationship("Conversation", back_populates="knowledge_repository")
    
    # 索引
    __table_args__ = (
        Index('idx_knowledge_repositories_owner_id', 'owner_id'),
        Index('idx_knowledge_repositories_name', 'name'),
    )

# 文档
class Document(Base):
    __tablename__ = "documents"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    repository_id = Column(UUID(as_uuid=True), ForeignKey("knowledge_repositories.id", ondelete="CASCADE"))
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_type = Column(String(50), nullable=False)  # pdf, docx, txt, md
    file_size = Column(Integer, nullable=False)
    file_path = Column(Text, nullable=False)
    content_hash = Column(String(64), nullable=False)  # SHA256 hash
    title = Column(String(500))
    author = Column(String(200))
    summary = Column(Text)
    chunk_count = Column(Integer, default=0)
    processing_status = Column(String(20), default="pending")  # pending, processing, completed, failed
    processing_error = Column(Text)
    doc_metadata = Column(JSONB, default={})
    uploaded_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    repository = relationship("KnowledgeRepository", back_populates="documents")
    uploader = relationship("User")
    chunks = relationship("DocumentChunk", back_populates="document", cascade="all, delete-orphan")
    
    # 索引
    __table_args__ = (
        Index('idx_documents_repository_id', 'repository_id'),
        Index('idx_documents_content_hash', 'content_hash'),
        Index('idx_documents_processing_status', 'processing_status'),
    )

# 文档分块
class DocumentChunk(Base):
    __tablename__ = "document_chunks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id", ondelete="CASCADE"))
    chunk_index = Column(Integer, nullable=False)
    content = Column(Text, nullable=False)
    content_length = Column(Integer, nullable=False)
    start_char = Column(Integer)
    end_char = Column(Integer)
    page_number = Column(Integer)
    section_title = Column(String(500))
    chunk_metadata = Column(JSONB, default={})
    embedding_vector = Column(LargeBinary)  # 存储向量的二进制数据
    embedding_model = Column(String(100))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    document = relationship("Document", back_populates="chunks")
    
    # 索引
    __table_args__ = (
        Index('idx_document_chunks_document_id', 'document_id'),
        Index('idx_document_chunks_chunk_index', 'chunk_index'),
    )

# 检索历史
class SearchHistory(Base):
    __tablename__ = "search_history"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    repository_id = Column(UUID(as_uuid=True), ForeignKey("knowledge_repositories.id"))
    query = Column(Text, nullable=False)
    results_count = Column(Integer, default=0)
    search_time_ms = Column(Integer)
    relevance_scores = Column(ARRAY(Float))  # 相关性分数数组
    retrieved_chunks = Column(ARRAY(UUID))  # 检索到的文档块ID数组
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    user = relationship("User")
    repository = relationship("KnowledgeRepository")
    
    # 索引
    __table_args__ = (
        Index('idx_search_history_user_id', 'user_id'),
        Index('idx_search_history_repository_id', 'repository_id'),
        Index('idx_search_history_created_at', 'created_at'),
    )

# 数据库依赖
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 创建表
def create_tables():
    Base.metadata.create_all(bind=engine)

# 元数据处理工具类
class MetadataHandler:
    # 压缩阈值（字节）
    COMPRESSION_THRESHOLD = 1024  # 1KB
    
    @staticmethod
    def process_metadata(metadata: dict) -> dict:
        """处理元数据，根据大小决定是否压缩"""
        if not metadata:
            return {}
        
        # 序列化为JSON字符串
        json_str = json.dumps(metadata, ensure_ascii=False)
        json_bytes = json_str.encode('utf-8')
        
        # 如果小于阈值，直接存储
        if len(json_bytes) < MetadataHandler.COMPRESSION_THRESHOLD:
            return {
                "data": metadata,
                "compressed": False,
                "size": len(json_bytes)
            }
        
        # 大于阈值，进行压缩
        try:
            compressed = gzip.compress(json_bytes)
            compressed_b64 = base64.b64encode(compressed).decode('ascii')
            
            return {
                "data": compressed_b64,
                "compressed": True,
                "original_size": len(json_bytes),
                "compressed_size": len(compressed),
                "compression_ratio": round(len(compressed) / len(json_bytes), 2)
            }
        except Exception as e:
            # 压缩失败，回退到原始数据但截断
            truncated_metadata = MetadataHandler._truncate_metadata(metadata)
            return {
                "data": truncated_metadata,
                "compressed": False,
                "truncated": True,
                "error": str(e)
            }
    
    @staticmethod
    def extract_metadata(stored_metadata: dict) -> dict:
        """从存储的元数据中提取原始数据"""
        if not stored_metadata or "data" not in stored_metadata:
            return {}
        
        # 如果未压缩，直接返回
        if not stored_metadata.get("compressed", False):
            return stored_metadata.get("data", {})
        
        # 解压缩数据
        try:
            compressed_b64 = stored_metadata["data"]
            compressed = base64.b64decode(compressed_b64)
            json_bytes = gzip.decompress(compressed)
            return json.loads(json_bytes.decode('utf-8'))
        except Exception as e:
            return {"error": f"解压缩失败: {str(e)}"}
    
    @staticmethod
    def _truncate_metadata(metadata: dict, max_length: int = 500) -> dict:
        """截断过长的元数据字段"""
        truncated = {}
        for key, value in metadata.items():
            if isinstance(value, str) and len(value) > max_length:
                truncated[key] = value[:max_length] + "...[截断]"
            elif isinstance(value, dict):
                truncated[key] = MetadataHandler._truncate_metadata(value, max_length)
            elif isinstance(value, list) and len(value) > 10:
                truncated[key] = value[:10] + ["...[截断]"]
            else:
                truncated[key] = value
        return truncated
    
    @staticmethod
    def get_metadata_stats(stored_metadata: dict) -> dict:
        """获取元数据统计信息"""
        if not stored_metadata:
            return {"size": 0, "compressed": False}
        
        return {
            "compressed": stored_metadata.get("compressed", False),
            "size": stored_metadata.get("compressed_size" if stored_metadata.get("compressed") else "size", 0),
            "original_size": stored_metadata.get("original_size"),
            "compression_ratio": stored_metadata.get("compression_ratio"),
            "truncated": stored_metadata.get("truncated", False)
        }

# 内容处理工具类
class ContentHandler:
    # 内容长度阈值
    LONG_CONTENT_THRESHOLD = 10000  # 10KB
    VERY_LONG_CONTENT_THRESHOLD = 50000  # 50KB
    
    @staticmethod
    def process_content(content: str, role: str = "user") -> dict:
        """
        智能处理消息内容
        返回: {
            "content": str,  # 实际存储的内容
            "is_truncated": bool,  # 是否被截断
            "original_length": int,  # 原始长度
            "content_type": str,  # "normal", "long", "very_long"
            "summary": str  # 如果被截断，提供摘要
        }
        """
        content_length = len(content)
        
        if content_length <= ContentHandler.LONG_CONTENT_THRESHOLD:
            # 正常长度，直接存储
            return {
                "content": content,
                "is_truncated": False,
                "original_length": content_length,
                "content_type": "normal",
                "summary": None
            }
        
        elif content_length <= ContentHandler.VERY_LONG_CONTENT_THRESHOLD:
            # 长内容，保留完整但标记
            return {
                "content": content,
                "is_truncated": False,
                "original_length": content_length,
                "content_type": "long",
                "summary": ContentHandler._generate_summary(content)
            }
        
        else:
            # 超长内容，需要截断
            if role == "user":
                # 用户消息：保留开头和结尾
                truncated = ContentHandler._smart_truncate_user_content(content)
            else:
                # 助手消息：保留开头部分
                truncated = ContentHandler._smart_truncate_assistant_content(content)
            
            return {
                "content": truncated,
                "is_truncated": True,
                "original_length": content_length,
                "content_type": "very_long",
                "summary": ContentHandler._generate_summary(content)
            }
    
    @staticmethod
    def _smart_truncate_user_content(content: str, max_length: int = 8000) -> str:
        """智能截断用户内容，保留开头和结尾"""
        if len(content) <= max_length:
            return content
        
        # 保留开头60%，结尾30%
        head_length = int(max_length * 0.6)
        tail_length = int(max_length * 0.3)
        
        head = content[:head_length]
        tail = content[-tail_length:] if tail_length > 0 else ""
        
        separator = f"\n\n... [已截断 {len(content) - head_length - tail_length} 字符] ...\n\n"
        
        return head + separator + tail
    
    @staticmethod
    def _smart_truncate_assistant_content(content: str, max_length: int = 8000) -> str:
        """智能截断助手内容，主要保留开头"""
        if len(content) <= max_length:
            return content
        
        # 尝试在句子边界截断
        truncated = content[:max_length]
        
        # 查找最后一个句号、问号或感叹号
        for punct in ['。', '！', '？', '.', '!', '?']:
            last_punct = truncated.rfind(punct)
            if last_punct > max_length * 0.8:  # 如果句号位置合理
                truncated = truncated[:last_punct + 1]
                break
        
        truncated += f"\n\n... [响应已截断，原长度: {len(content)} 字符] ..."
        
        return truncated
    
    @staticmethod
    def _generate_summary(content: str, max_summary_length: int = 200) -> str:
        """生成内容摘要"""
        # 简单的摘要生成：取开头部分
        if len(content) <= max_summary_length:
            return content
        
        summary = content[:max_summary_length]
        
        # 尝试在词边界截断
        last_space = summary.rfind(' ')
        last_chinese = summary.rfind('，')
        
        boundary = max(last_space, last_chinese)
        if boundary > max_summary_length * 0.7:
            summary = summary[:boundary]
        
        return summary + "..."
    
    @staticmethod
    def get_content_info(message) -> dict:
        """获取消息内容的详细信息"""
        content = message.content
        metadata = MetadataHandler.extract_metadata(message.msg_metadata or {})
        
        return {
            "message_id": str(message.id),
            "role": message.role,
            "content_length": len(content),
            "is_truncated": metadata.get("content_info", {}).get("is_truncated", False),
            "original_length": metadata.get("content_info", {}).get("original_length", len(content)),
            "content_type": metadata.get("content_info", {}).get("content_type", "normal"),
            "summary": metadata.get("content_info", {}).get("summary"),
            "created_at": message.created_at
        } 