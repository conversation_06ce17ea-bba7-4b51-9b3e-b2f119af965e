package com.superwu.application.user.dto;

import jakarta.validation.constraints.Size;

/**
 * 用户更新请求DTO
 */
public record UserUpdateRequest(
    String avatarUrl,
    
    @Size(max = 500, message = "<PERSON><PERSON> must not exceed 500 characters")
    String bio,
    
    @Size(max = 50, message = "GitHub username must not exceed 50 characters")
    String githubUsername,
    
    String websiteUrl
) {} 