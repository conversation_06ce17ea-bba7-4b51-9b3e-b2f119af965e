import { create } from 'zustand'
import { persist, subscribeWithSelector } from 'zustand/middleware'
import api from '../services/api'

const useChatStore = create(
  persist(
    (set, get) => ({
      // 聊天相关状态
      conversations: [],
      currentConversationId: null,
      isLoading: false,
      
      // 模型相关状态
      models: [],
      currentModel: null,
      
      // 初始化数据
      initialize: async () => {
        try {
          const models = await api.getModels()
          set({ models })
          
          // 设置默认模型为第一个启用的模型
          const enabledModel = models.find(m => m.enabled)
          if (enabledModel && !get().currentModel) {
            set({ currentModel: enabledModel })
          }
        } catch (error) {
          console.error('初始化失败:', error)
        }
      },
      
      // 聊天功能
      createConversation: () => {
        const newConversation = {
          id: crypto.randomUUID(),
          title: '新对话',
          messages: [],
          createdAt: new Date().toISOString(),
          modelId: get().currentModel?.id
        }
        
        set(state => ({
          conversations: [newConversation, ...state.conversations],
          currentConversationId: newConversation.id
        }))
        
        return newConversation.id
      },
      
      setCurrentConversation: (id) => {
        set({ currentConversationId: id })
      },
      
      deleteConversation: (id) => {
        set(state => {
          const newConversations = state.conversations.filter(c => c.id !== id)
          const newCurrentId = state.currentConversationId === id 
            ? (newConversations[0]?.id || null) 
            : state.currentConversationId
            
          return {
            conversations: newConversations,
            currentConversationId: newCurrentId
          }
        })
      },
      
      sendMessage: async (content, ragOptions = null) => {
        const state = get()
        if (!state.currentModel || !content.trim()) return
        
        set({ isLoading: true })
        
        try {
          // 确保有当前对话
          let conversationId = state.currentConversationId
          if (!conversationId) {
            conversationId = get().createConversation()
          }
          
          // 添加用户消息
          const userMessage = {
            id: crypto.randomUUID(),
            role: 'user',
            content: content.trim(),
            timestamp: new Date().toISOString()
          }
          
          set(state => ({
            conversations: state.conversations.map(conv =>
              conv.id === conversationId
                ? { 
                    ...conv, 
                    messages: [...conv.messages, userMessage],
                    title: conv.messages.length === 0 ? content.slice(0, 20) + '...' : conv.title
                  }
                : conv
            )
          }))
          
          // 获取当前对话的消息历史
          const conversation = get().conversations.find(c => c.id === conversationId)
          const messages = conversation.messages.map(msg => ({
            role: msg.role,
            content: msg.content
          }))
          
          // 创建临时的助手消息用于流式更新
          const assistantMessageId = crypto.randomUUID()
          const tempAssistantMessage = {
            id: assistantMessageId,
            role: 'assistant',
            content: '',
            timestamp: new Date().toISOString(),
            model: state.currentModel.name,
            isStreaming: true
          }
          
          // 添加临时助手消息
          set(state => ({
            conversations: state.conversations.map(conv =>
              conv.id === conversationId
                ? { ...conv, messages: [...conv.messages, tempAssistantMessage] }
                : conv
            )
          }))
          
          let fullContent = ''
          let lastUsage = null
          
          // 根据是否启用RAG选择不同的API
          if (ragOptions && ragOptions.enabled && ragOptions.repositoryId) {
            // 使用RAG流式API
            const { knowledgeApi } = await import('../services/api')
            await knowledgeApi.ragChatStream(
              {
                messages: messages, // 传递完整的消息历史
                model_id: state.currentModel.id,
                conversation_id: conversationId,
                repository_id: ragOptions.repositoryId,
                top_k: 5,
                threshold: 0.7
              },
            // onChunk - 处理每个数据块
            (chunk) => {
              if (chunk.content) {
                fullContent += chunk.content
                
                // 更新助手消息内容
                set(state => ({
                  conversations: state.conversations.map(conv =>
                    conv.id === conversationId
                      ? {
                          ...conv,
                          messages: conv.messages.map(msg =>
                            msg.id === assistantMessageId
                              ? { ...msg, content: fullContent }
                              : msg
                          )
                        }
                      : conv
                  )
                }))
              }
              
              // 如果收到后端返回的真实conversationId，更新本地对话ID
              if (chunk.conversationId && chunk.conversationId !== conversationId) {
                const oldConversationId = conversationId
                conversationId = chunk.conversationId
                
                set(state => ({
                  conversations: state.conversations.map(conv =>
                    conv.id === oldConversationId
                      ? { ...conv, id: conversationId }
                      : conv
                  ),
                  currentConversationId: conversationId
                }))
              }
              
              if (chunk.usage) {
                lastUsage = chunk.usage
              }
            },
            // onComplete - 流完成
            () => {
              set(state => ({
                conversations: state.conversations.map(conv =>
                  conv.id === conversationId
                    ? {
                        ...conv,
                        messages: conv.messages.map(msg =>
                          msg.id === assistantMessageId
                            ? { 
                                ...msg, 
                                isStreaming: false,
                                usage: lastUsage 
                              }
                            : msg
                        )
                      }
                    : conv
                )
              }))
              set({ isLoading: false })
            },
            // onError - 错误处理
            (error) => {
              console.error('流式发送消息失败:', error)
              
              // 如果没有收到任何内容，显示错误消息
              if (!fullContent) {
                set(state => ({
                  conversations: state.conversations.map(conv =>
                    conv.id === conversationId
                      ? {
                          ...conv,
                          messages: conv.messages.map(msg =>
                            msg.id === assistantMessageId
                              ? {
                                  ...msg,
                                  content: `抱歉，发生了错误：${error.message}`,
                                  isError: true,
                                  isStreaming: false
                                }
                              : msg
                          )
                        }
                      : conv
                  )
                }))
              } else {
                // 如果已经有部分内容，只是停止流式传输
                set(state => ({
                  conversations: state.conversations.map(conv =>
                    conv.id === conversationId
                      ? {
                          ...conv,
                          messages: conv.messages.map(msg =>
                            msg.id === assistantMessageId
                              ? { ...msg, isStreaming: false }
                              : msg
                          )
                        }
                      : conv
                  )
                }))
              }
              set({ isLoading: false })
            }
          )
          } else {
            // 使用普通流式API
            await api.sendMessageStream(
              state.currentModel.id,
              messages,
              conversationId, // 传递对话ID
              // onChunk - 处理每个数据块
              (chunk) => {
                if (chunk.content) {
                  fullContent += chunk.content
                  
                  // 更新助手消息内容
                  set(state => ({
                    conversations: state.conversations.map(conv =>
                      conv.id === conversationId
                        ? {
                            ...conv,
                            messages: conv.messages.map(msg =>
                              msg.id === assistantMessageId
                                ? { ...msg, content: fullContent }
                                : msg
                            )
                          }
                        : conv
                    )
                  }))
                }
                
                // 如果收到后端返回的真实conversationId，更新本地对话ID
                if (chunk.conversationId && chunk.conversationId !== conversationId) {
                  const oldConversationId = conversationId
                  conversationId = chunk.conversationId
                  
                  set(state => ({
                    conversations: state.conversations.map(conv =>
                      conv.id === oldConversationId
                        ? { ...conv, id: conversationId }
                        : conv
                    ),
                    currentConversationId: conversationId
                  }))
                }
                
                if (chunk.usage) {
                  lastUsage = chunk.usage
                }
              },
              // onComplete - 流完成
              () => {
                set(state => ({
                  conversations: state.conversations.map(conv =>
                    conv.id === conversationId
                      ? {
                          ...conv,
                          messages: conv.messages.map(msg =>
                            msg.id === assistantMessageId
                              ? { 
                                  ...msg, 
                                  isStreaming: false,
                                  usage: lastUsage 
                                }
                              : msg
                          )
                        }
                      : conv
                  )
                }))
                set({ isLoading: false })
              },
              // onError - 错误处理
              (error) => {
                console.error('流式发送消息失败:', error)
                
                // 如果没有收到任何内容，显示错误消息
                if (!fullContent) {
                  set(state => ({
                    conversations: state.conversations.map(conv =>
                      conv.id === conversationId
                        ? {
                            ...conv,
                            messages: conv.messages.map(msg =>
                              msg.id === assistantMessageId
                                ? {
                                    ...msg,
                                    content: `抱歉，发生了错误：${error.message}`,
                                    isError: true,
                                    isStreaming: false
                                  }
                                : msg
                            )
                          }
                        : conv
                    )
                  }))
                } else {
                  // 如果已经有部分内容，只是停止流式传输
                  set(state => ({
                    conversations: state.conversations.map(conv =>
                      conv.id === conversationId
                        ? {
                            ...conv,
                            messages: conv.messages.map(msg =>
                              msg.id === assistantMessageId
                                ? { ...msg, isStreaming: false }
                                : msg
                            )
                          }
                        : conv
                    )
                  }))
                }
                set({ isLoading: false })
              }
            )
          }
          
        } catch (error) {
          console.error('发送消息失败:', error)
          
          // 添加错误消息
          const errorMessage = {
            id: (Date.now() + 1).toString(),
            role: 'assistant',
            content: `抱歉，发生了错误：${error.response?.data?.error || error.message}`,
            timestamp: new Date().toISOString(),
            isError: true
          }
          
          set(state => ({
            conversations: state.conversations.map(conv =>
              conv.id === state.currentConversationId
                ? { ...conv, messages: [...conv.messages, errorMessage] }
                : conv
            )
          }))
          set({ isLoading: false })
        }
      },
      
      // 模型管理
      setCurrentModel: (model) => {
        set({ currentModel: model })
      },
      
      refreshModels: async () => {
        try {
          const models = await api.getModels()
          set({ models })
        } catch (error) {
          console.error('刷新模型列表失败:', error)
        }
      },
      
      // 获取当前对话
      getCurrentConversation: () => {
        const state = get()
        return state.conversations.find(c => c.id === state.currentConversationId)
      }
    }),
    {
      name: 'chat-storage',
      partialize: (state) => ({
        conversations: state.conversations.map(conv => ({
          ...conv,
          messages: conv.messages.map(msg => ({
            ...msg,
            // 过滤掉图片的base64数据，只保留基本信息
            attachments: msg.attachments ? msg.attachments.map(att => ({
              type: att.type,
              name: att.name,
              file_size: att.file_size,
              // 不保存 base64_data 和 url 到 localStorage
            })) : undefined
          }))
        })),
        currentConversationId: state.currentConversationId,
        currentModel: state.currentModel
      })
    }
  )
)

export default useChatStore 