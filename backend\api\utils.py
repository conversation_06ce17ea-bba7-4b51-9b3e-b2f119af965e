from sqlalchemy.orm import Session
from fastapi import HTTPException
from typing import Dict, Any, List
from datetime import datetime

from database import get_db, SessionLocal, ContentHandler, MetadataHandler
from database import User as DBUser, ModelConfig as DBModelConfig, Conversation as DBConversation, Message as DBMessage

# 默认用户ID
DEFAULT_USER_ID = "550e8400-e29b-41d4-a716-446655440000"

# 帮助函数：创建对话（支持RAG参数）
def create_conversation_with_rag(user_id: str, model_id: str, title: str, db: Session, 
                                rag_enabled: bool = False, rag_repository_id: str = None,
                                rag_top_k: int = 5, rag_threshold: float = 0.7,
                                system_prompt: str = None) -> DBConversation:
    """创建新对话，支持RAG参数"""
    conversation = DBConversation(
        user_id=user_id,
        model_id=model_id,
        title=title,
        rag_enabled=rag_enabled,
        rag_repository_id=rag_repository_id,
        rag_top_k=rag_top_k,
        rag_threshold=rag_threshold,
        system_prompt=system_prompt
    )
    
    db.add(conversation)
    db.commit()
    db.refresh(conversation)
    
    return conversation

def get_model_config(model_id: str, db: Session) -> Dict[str, Any]:
    """获取模型配置"""
    # 兼容旧的字符串ID
    if model_id == "qwen":
        model_id = "550e8400-e29b-41d4-a716-446655440001"
    
    model = db.query(DBModelConfig).filter(DBModelConfig.id == model_id).first()
    if not model:
        raise HTTPException(status_code=404, detail="模型未找到")
    
    if not model.enabled:
        raise HTTPException(status_code=400, detail="模型已禁用")
    
    return {
        "id": str(model.id),
        "name": model.name,
        "provider": model.provider,
        "apiEndpoint": model.api_endpoint,
        "apiKey": model.api_key_encrypted,
        "model": model.model,
        "parameters": {
            "temperature": model.parameters.get("temperature", 0.7),
            "max_tokens": model.parameters.get("max_tokens", 2000),
            "top_p": model.parameters.get("top_p", 0.8)
        }
    }

def save_message(conversation_id: str, role: str, content: str, model_id: str = None,
                db: Session = None, msg_metadata: Dict = None, content_type: str = "text",
                attachments: List = None) -> DBMessage:
    """保存消息到数据库"""
    try:
        # 处理内容
        content_info = ContentHandler.process_content(content, role)
        
        # 处理元数据
        processed_metadata = {}
        if msg_metadata:
            processed_metadata = MetadataHandler.process_metadata(msg_metadata)
        
        # 创建消息（Message表不包含model_id字段）
        message = DBMessage(
            conversation_id=conversation_id,
            role=role,
            content=content_info["content"],
            content_type=content_type,
            attachments=attachments or [],
            token_count=len(content) // 4,  # 简单估算
            msg_metadata=processed_metadata
        )
        
        db.add(message)
        
        # 更新对话统计 - 使用原始SQL更新，避免会话问题
        try:
            conversation = db.query(DBConversation).filter(DBConversation.id == conversation_id).first()
            if conversation:
                conversation.message_count += 1
                conversation.updated_at = datetime.now()
        except Exception as conv_error:
            print(f"更新对话统计失败: {conv_error}")
            # 不影响消息保存，继续执行
        
        db.commit()
        db.refresh(message)
        
        return message
        
    except Exception as e:
        db.rollback()
        print(f"保存消息失败: {e}")
        raise e

def init_default_data():
    """初始化默认数据"""
    db = SessionLocal()
    try:
        # 检查默认用户是否存在
        existing_user = db.query(DBUser).filter(DBUser.id == DEFAULT_USER_ID).first()
        if not existing_user:
            default_user = DBUser(
                id=DEFAULT_USER_ID,
                username="默认用户",
                email="<EMAIL>"
            )
            db.add(default_user)
            print("✅ 创建默认用户")
        
        # 检查默认模型配置是否存在
        default_model_id = "550e8400-e29b-41d4-a716-446655440001"
        existing_model = db.query(DBModelConfig).filter(DBModelConfig.id == default_model_id).first()
        if not existing_model:
            default_model = DBModelConfig(
                id=default_model_id,
                name="通义千问",
                provider="dashscope",
                api_endpoint="https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",
                api_key_encrypted="your_api_key_here",
                model="qwen-turbo",
                enabled=True,
                parameters={
                    "temperature": 0.7,
                    "max_tokens": 2000,
                    "top_p": 0.8
                },
                created_by=DEFAULT_USER_ID
            )
            db.add(default_model)
            print("✅ 创建默认模型配置")

        # 检查多模态模型配置是否存在
        multimodal_model_id = "550e8400-e29b-41d4-a716-446655440002"
        existing_multimodal = db.query(DBModelConfig).filter(DBModelConfig.id == multimodal_model_id).first()
        if not existing_multimodal:
            multimodal_model = DBModelConfig(
                id=multimodal_model_id,
                name="通义千问VL-Plus",
                provider="dashscope",
                api_endpoint="https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
                api_key_encrypted="your_api_key_here",
                model="qwen-vl-plus",
                enabled=True,
                parameters={
                    "temperature": 0.7,
                    "max_tokens": 2000,
                    "top_p": 0.8
                },
                created_by=DEFAULT_USER_ID
            )
            db.add(multimodal_model)
            print("✅ 创建多模态模型配置")
        
        db.commit()
        print("✅ 默认数据初始化完成")
        
    except Exception as e:
        db.rollback()
        print(f"❌ 默认数据初始化失败: {e}")
    finally:
        db.close() 