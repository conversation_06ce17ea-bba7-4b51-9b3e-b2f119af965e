from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from sqlalchemy import and_
from typing import Optional

from database import get_db
from database import User as DBUser, Conversation as DBConversation, Message as DBMessage
from chat_context import ContextManager
from .utils import DEFAULT_USER_ID

router = APIRouter()

# 获取对话列表
@router.get("/api/conversations")
async def get_conversations(db: Session = Depends(get_db), limit: int = 50, offset: int = 0):
    conversations = db.query(DBConversation).filter(
        and_(DBConversation.user_id == DEFAULT_USER_ID, DBConversation.is_archived == False)
    ).order_by(DBConversation.updated_at.desc()).offset(offset).limit(limit).all()
    
    result = []
    for conv in conversations:
        result.append({
            "id": str(conv.id),
            "title": conv.title,
            "modelId": str(conv.model_id) if conv.model_id else None,
            "messageCount": conv.message_count,
            "createdAt": conv.created_at.isoformat(),
            "updatedAt": conv.updated_at.isoformat()
        })
    
    return result

# 创建新对话
@router.post("/api/conversations")
async def create_conversation(
    title: str = "新对话",
    model_id: Optional[str] = None,
    system_prompt: Optional[str] = None,
    db: Session = Depends(get_db)
):
    user = db.query(DBUser).filter(DBUser.id == DEFAULT_USER_ID).first()
    if not user:
        raise HTTPException(status_code=404, detail="用户未找到")
    
    new_conversation = DBConversation(
        user_id=user.id,
        title=title,
        model_id=model_id if model_id else None,
        system_prompt=system_prompt
    )
    
    db.add(new_conversation)
    db.commit()
    db.refresh(new_conversation)
    
    return {
        "success": True,
        "conversation": {
            "id": str(new_conversation.id),
            "title": new_conversation.title,
            "modelId": str(new_conversation.model_id) if new_conversation.model_id else None,
            "systemPrompt": new_conversation.system_prompt,
            "createdAt": new_conversation.created_at.isoformat()
        }
    }

# 获取对话详情和消息历史
@router.get("/api/conversations/{conversation_id}")
async def get_conversation(conversation_id: str, db: Session = Depends(get_db)):
    conversation = db.query(DBConversation).filter(DBConversation.id == conversation_id).first()
    if not conversation:
        raise HTTPException(status_code=404, detail="对话未找到")
    
    messages = db.query(DBMessage).filter(DBMessage.conversation_id == conversation_id).order_by(DBMessage.created_at).all()
    
    return {
        "id": str(conversation.id),
        "title": conversation.title,
        "modelId": str(conversation.model_id) if conversation.model_id else None,
        "systemPrompt": conversation.system_prompt,
        "messages": [
            {
                "id": str(msg.id),
                "role": msg.role,
                "content": msg.content,
                "createdAt": msg.created_at.isoformat(),
                "tokenCount": msg.token_count
            }
            for msg in messages
        ],
        "createdAt": conversation.created_at.isoformat(),
        "updatedAt": conversation.updated_at.isoformat()
    }

# 删除对话
@router.delete("/api/conversations/{conversation_id}")
async def delete_conversation(conversation_id: str, db: Session = Depends(get_db)):
    conversation = db.query(DBConversation).filter(DBConversation.id == conversation_id).first()
    if not conversation:
        raise HTTPException(status_code=404, detail="对话未找到")
    
    # 清理上下文缓存
    context = await ContextManager.get_or_create_context(conversation_id)
    await context.clear_context()
    
    db.delete(conversation)
    db.commit()
    return {"success": True} 