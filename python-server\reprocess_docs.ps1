# 重新处理文档的PowerShell脚本

Write-Host "🔧 开始重新处理文档..." -ForegroundColor Green

# 1. 获取文档列表
Write-Host "📋 获取文档列表..." -ForegroundColor Yellow
$response = Invoke-RestMethod -Uri "http://localhost:3002/api/knowledge/repositories/d356ecae-6d96-4b0c-a7dd-51db9d81c16a/documents" -Method GET

Write-Host "找到 $($response.Count) 个文档:" -ForegroundColor Cyan
foreach ($doc in $response) {
    Write-Host "  - $($doc.filename) (ID: $($doc.id), 分块数: $($doc.chunk_count))" -ForegroundColor White
    
    # 如果分块数为0，则重新处理
    if ($doc.chunk_count -eq 0) {
        Write-Host "    🔄 重新处理中..." -ForegroundColor Yellow
        
        try {
            $reprocessResponse = Invoke-RestMethod -Uri "http://localhost:3002/api/knowledge/documents/$($doc.id)/reprocess" -Method POST -ContentType "application/json"
            
            if ($reprocessResponse.success) {
                Write-Host "    ✅ 处理成功! 生成 $($reprocessResponse.chunk_count) 个块" -ForegroundColor Green
            } else {
                Write-Host "    ❌ 处理失败: $($reprocessResponse.message)" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "    ❌ 处理失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "    ✅ 已处理 ($($doc.chunk_count) 个块)" -ForegroundColor Green
    }
}

Write-Host "🎉 处理完成!" -ForegroundColor Green
