package com.superwu.web.controller;

import com.superwu.application.user.UserService;
import com.superwu.application.user.dto.UserCreateRequest;
import com.superwu.application.user.dto.UserResponse;
import com.superwu.application.user.dto.UserUpdateRequest;
import com.superwu.web.dto.ApiResponse;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 用户管理控制器
 * 提供用户相关的REST API
 * 暂时禁用以解决依赖问题
 */
//@RestController
//@RequestMapping("/api/users")
public class UserController {
    
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);
    
    private final UserService userService;
    
    public UserController(UserService userService) {
        this.userService = userService;
    }
    
    /**
     * 创建用户
     */
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public Mono<ApiResponse<UserResponse>> createUser(@Valid @RequestBody UserCreateRequest request) {
        logger.debug("Creating user: {}", request.username());
        
        return userService.createUser(request)
            .map(user -> ApiResponse.success("User created successfully", user))
            .doOnError(error -> logger.error("Failed to create user", error));
    }
    
    /**
     * 根据ID获取用户
     */
    @GetMapping("/{id}")
    public Mono<ApiResponse<UserResponse>> getUserById(@PathVariable String id) {
        logger.debug("Getting user by ID: {}", id);
        
        return userService.findById(id)
            .map(user -> ApiResponse.success("User found", user))
            .switchIfEmpty(Mono.just(ApiResponse.error("User not found")))
            .doOnError(error -> logger.error("Failed to get user by ID: {}", id, error));
    }
    
    /**
     * 根据用户名获取用户
     */
    @GetMapping("/username/{username}")
    public Mono<ApiResponse<UserResponse>> getUserByUsername(@PathVariable String username) {
        logger.debug("Getting user by username: {}", username);
        
        return userService.findByUsername(username)
            .map(user -> ApiResponse.success("User found", user))
            .switchIfEmpty(Mono.just(ApiResponse.error("User not found")))
            .doOnError(error -> logger.error("Failed to get user by username: {}", username, error));
    }
    
    /**
     * 更新用户资料
     */
    @PutMapping("/{id}")
    public Mono<ApiResponse<UserResponse>> updateUser(
            @PathVariable String id,
            @Valid @RequestBody UserUpdateRequest request) {
        logger.debug("Updating user: {}", id);
        
        return userService.updateUser(id, request)
            .map(user -> ApiResponse.success("User updated successfully", user))
            .doOnError(error -> logger.error("Failed to update user: {}", id, error));
    }
    
    /**
     * 获取所有激活用户
     */
    @GetMapping("/active")
    public Mono<ApiResponse<Flux<UserResponse>>> getAllActiveUsers() {
        logger.debug("Getting all active users");
        
        Flux<UserResponse> users = userService.findAllActive();
        return Mono.just(ApiResponse.success("Active users retrieved", users));
    }
} 