#!/usr/bin/env python3
"""
修复文档处理工具
重新处理已上传但未分块的文档
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db, KnowledgeRepository, Document, DocumentChunk
from rag_core import rag_processor
from sqlalchemy.orm import Session
from pathlib import Path

async def fix_document_processing():
    """修复文档处理问题"""
    
    # 获取数据库会话
    db = next(get_db())
    
    try:
        print("🔧 开始修复文档处理...")
        print("=" * 50)
        
        # 1. 查找所有未处理的文档（chunk_count = 0）
        unprocessed_docs = db.query(Document).filter(
            Document.chunk_count == 0
        ).all()
        
        print(f"📄 找到 {len(unprocessed_docs)} 个未处理的文档:")
        for doc in unprocessed_docs:
            print(f"  - {doc.filename} (状态: {doc.processing_status})")
            print(f"    文件路径: {doc.file_path}")
            print(f"    仓库ID: {doc.repository_id}")
        
        if not unprocessed_docs:
            print("✅ 没有找到需要处理的文档")
            return
        
        # 2. 重新处理每个文档
        for doc in unprocessed_docs:
            print(f"\n🔄 开始处理文档: {doc.filename}")
            
            try:
                # 检查文件是否存在
                file_path = Path(doc.file_path)
                if not file_path.exists():
                    print(f"❌ 文件不存在: {file_path}")
                    doc.processing_status = "failed"
                    doc.processing_error = "文件不存在"
                    db.commit()
                    continue
                
                # 获取文件类型
                file_type = file_path.suffix.lower().lstrip('.')
                print(f"📋 文件类型: {file_type}")
                
                # 重新处理文档
                print("🔍 提取文本内容...")
                text_content, metadata = rag_processor.doc_processor.extract_text(str(file_path), file_type)
                print(f"📝 提取到文本长度: {len(text_content)} 字符")
                
                if not text_content.strip():
                    print("⚠️ 文档内容为空")
                    doc.processing_status = "failed"
                    doc.processing_error = "文档内容为空"
                    db.commit()
                    continue
                
                # 更新文档元数据
                doc.title = metadata.get('title') or Path(doc.filename).stem
                doc.author = metadata.get('author', '')
                doc.doc_metadata = metadata
                
                # 获取知识库配置
                repository = db.query(KnowledgeRepository).filter(
                    KnowledgeRepository.id == doc.repository_id
                ).first()
                
                if not repository:
                    print("❌ 找不到对应的知识库")
                    continue
                
                # 文本分块
                print("✂️ 开始文本分块...")
                chunks = rag_processor.text_chunker.split_by_sentences(
                    text_content,
                    max_chunk_size=repository.chunk_size,
                    overlap=repository.chunk_overlap
                )
                print(f"📦 生成 {len(chunks)} 个文本块")
                
                if not chunks:
                    print("⚠️ 没有生成任何文本块")
                    doc.processing_status = "failed"
                    doc.processing_error = "文本分块失败"
                    db.commit()
                    continue
                
                # 向量化处理
                print("🧮 开始向量化处理...")
                chunk_texts = [chunk["content"] for chunk in chunks]
                embeddings = rag_processor.embedding_manager.encode_texts(chunk_texts)
                print(f"✅ 生成 {len(embeddings)} 个向量")
                
                # 删除旧的文档块（如果有）
                old_chunks = db.query(DocumentChunk).filter(
                    DocumentChunk.document_id == doc.id
                ).all()
                for old_chunk in old_chunks:
                    db.delete(old_chunk)
                
                # 保存新的文档块
                print("💾 保存文档块...")
                import pickle
                for i, (chunk_info, embedding) in enumerate(zip(chunks, embeddings)):
                    chunk = DocumentChunk(
                        document_id=doc.id,
                        chunk_index=i,
                        content=chunk_info["content"],
                        content_length=len(chunk_info["content"]),
                        start_char=chunk_info["start_char"],
                        end_char=chunk_info["end_char"],
                        embedding_vector=pickle.dumps(embedding),
                        embedding_model=rag_processor.embedding_manager.model_name,
                        metadata=chunk_info
                    )
                    db.add(chunk)
                
                # 更新文档统计信息
                doc.chunk_count = len(chunks)
                doc.processing_status = "completed"
                doc.processing_error = None
                
                # 更新知识库统计信息
                repository.total_chunks = db.query(DocumentChunk).join(Document).filter(
                    Document.repository_id == repository.id
                ).count()
                
                db.commit()
                
                print(f"✅ 文档处理完成: {doc.filename}")
                print(f"   - 生成块数: {len(chunks)}")
                print(f"   - 状态: {doc.processing_status}")
                
            except Exception as e:
                print(f"❌ 处理文档失败: {e}")
                import traceback
                traceback.print_exc()
                
                # 更新错误状态
                doc.processing_status = "failed"
                doc.processing_error = str(e)
                db.commit()
        
        print("\n🎉 文档处理修复完成!")
        
        # 3. 显示最终统计
        print("\n📊 最终统计:")
        repositories = db.query(KnowledgeRepository).all()
        for repo in repositories:
            doc_count = db.query(Document).filter(Document.repository_id == repo.id).count()
            chunk_count = db.query(DocumentChunk).join(Document).filter(
                Document.repository_id == repo.id
            ).count()
            print(f"  📚 {repo.name}:")
            print(f"    - 文档数: {doc_count}")
            print(f"    - 块数: {chunk_count}")
        
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(fix_document_processing())
