#!/usr/bin/env python3
"""
知识库搜索测试工具
用于测试和调试知识库检索功能
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db, KnowledgeRepository, Document, DocumentChunk
from rag_core import rag_processor
from sqlalchemy.orm import Session

async def test_knowledge_search():
    """测试知识库搜索功能"""
    
    # 获取数据库会话
    db = next(get_db())
    
    try:
        print("🔍 开始测试知识库搜索功能...")
        print("=" * 50)
        
        # 1. 列出所有知识库
        repositories = db.query(KnowledgeRepository).all()
        print(f"📚 找到 {len(repositories)} 个知识库:")
        for repo in repositories:
            print(f"  - {repo.name} (ID: {repo.id})")
            print(f"    文档数量: {repo.document_count}")
            print(f"    总块数: {repo.total_chunks}")
        
        if not repositories:
            print("❌ 没有找到任何知识库")
            return
        
        # 2. 选择第一个知识库进行测试
        test_repo = repositories[0]
        print(f"\n🎯 使用知识库: {test_repo.name}")
        
        # 3. 列出该知识库中的文档
        documents = db.query(Document).filter(Document.repository_id == test_repo.id).all()
        print(f"📄 该知识库中有 {len(documents)} 个文档:")
        for doc in documents:
            print(f"  - {doc.filename} (状态: {doc.processing_status})")
            print(f"    块数: {doc.chunk_count}")
        
        # 4. 检查文档块
        total_chunks = db.query(DocumentChunk).join(Document).filter(
            Document.repository_id == test_repo.id
        ).count()
        print(f"📦 总共有 {total_chunks} 个文档块")
        
        # 5. 测试搜索
        test_queries = [
            "WL-210",
            "分散剂",
            "使用方法",
            "注意事项",
            "技术参数"
        ]
        
        print(f"\n🔍 开始搜索测试...")
        for query in test_queries:
            print(f"\n查询: '{query}'")
            try:
                results = await rag_processor.search_knowledge(
                    query=query,
                    repository_id=str(test_repo.id),
                    top_k=3,
                    threshold=0.5,
                    user_id="550e8400-e29b-41d4-a716-446655440000",
                    db=db
                )
                
                print(f"  找到 {len(results)} 个相关结果:")
                for i, result in enumerate(results, 1):
                    print(f"    {i}. 相关度: {result['relevance_score']:.3f}")
                    print(f"       文档: {result['document_title']}")
                    print(f"       内容预览: {result['content'][:100]}...")
                    print()
                    
            except Exception as e:
                print(f"  ❌ 搜索失败: {e}")
        
        print("✅ 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_knowledge_search())
