#!/usr/bin/env python3
"""
数据库迁移脚本：为消息表添加图片支持
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import text
from database import get_db, engine

def migrate_database():
    """执行数据库迁移"""
    
    print("🔄 开始数据库迁移：添加图片支持...")
    
    try:
        with engine.connect() as conn:
            # 检查是否已经存在新字段
            result = conn.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'messages' 
                AND column_name IN ('content_type', 'attachments')
            """))
            
            existing_columns = [row[0] for row in result.fetchall()]
            
            # 添加 content_type 字段
            if 'content_type' not in existing_columns:
                print("📝 添加 content_type 字段...")
                conn.execute(text("""
                    ALTER TABLE messages 
                    ADD COLUMN content_type VARCHAR(20) DEFAULT 'text'
                """))
                print("✅ content_type 字段添加成功")
            else:
                print("ℹ️ content_type 字段已存在")
            
            # 添加 attachments 字段
            if 'attachments' not in existing_columns:
                print("📝 添加 attachments 字段...")
                conn.execute(text("""
                    ALTER TABLE messages 
                    ADD COLUMN attachments JSONB DEFAULT '[]'::jsonb
                """))
                print("✅ attachments 字段添加成功")
            else:
                print("ℹ️ attachments 字段已存在")
            
            # 提交更改
            conn.commit()
            
            print("🎉 数据库迁移完成！")
            
            # 验证迁移结果
            print("\n📊 验证迁移结果...")
            result = conn.execute(text("""
                SELECT column_name, data_type, column_default
                FROM information_schema.columns 
                WHERE table_name = 'messages' 
                AND column_name IN ('content_type', 'attachments')
                ORDER BY column_name
            """))
            
            for row in result.fetchall():
                print(f"  - {row[0]}: {row[1]} (默认值: {row[2]})")
            
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = migrate_database()
    if success:
        print("\n✅ 迁移成功完成！现在可以使用图片聊天功能了。")
    else:
        print("\n❌ 迁移失败！请检查错误信息。")
        sys.exit(1)
