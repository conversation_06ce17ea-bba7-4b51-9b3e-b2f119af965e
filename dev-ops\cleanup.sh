#!/bin/bash

echo "🗑️  清理多模型聊天机器人数据"
echo "=========================="

cd "$(dirname "$0")"

read -p "⚠️  这将删除所有数据库数据和缓存，确认吗？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "停止并删除容器、网络和数据卷..."
    docker-compose down -v
    
    echo "清理未使用的Docker资源..."
    docker system prune -f
    
    echo ""
    echo "✅ 清理完成！"
    echo ""
    echo "💡 重新启动: ./start-all.sh"
else
    echo "❌ 已取消清理操作"
fi 