@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义样式 */
@layer base {
  html, body, #root {
    height: 100%;
    overflow: hidden;
  }
  
  body {
    @apply font-sans antialiased;
  }
}

@layer components {
  .chat-bubble {
    @apply max-w-3xl mx-auto mb-4 p-4 rounded-lg shadow-sm;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
  
  .chat-bubble.user {
    @apply bg-blue-500 text-white ml-8;
  }
  
  .chat-bubble.assistant {
    @apply bg-gray-100 text-gray-800 mr-8;
  }
  
  .model-card {
    @apply bg-white rounded-lg shadow-md p-6 border border-gray-200 hover:shadow-lg transition-shadow;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }
  
  .btn-primary {
    @apply bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-700 font-medium py-2 px-4 rounded-md transition-colors;
  }
  
  .btn-danger {
    @apply bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-md transition-colors;
  }
}

/* 滚动条样式 */
.scrollbar-thin::-webkit-scrollbar {
  width: 8px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Firefox滚动条 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* 流式输出动画 */
@keyframes typing-cursor {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.typing-cursor {
  animation: typing-cursor 1s infinite;
}

.streaming-message {
  position: relative;
}

.streaming-message::after {
  content: '';
  display: inline-block;
  width: 2px;
  height: 1.2em;
  background-color: currentColor;
  margin-left: 2px;
  animation: typing-cursor 1s infinite;
} 