from fastapi import HTTPException
from typing import AsyncGenerator, Dict, Any
import httpx
import json

# 根据模型提供商分发流式请求
async def stream_chat_response(model_config: Dict[str, Any], messages) -> AsyncGenerator[str, None]:
    """根据模型提供商分发流式请求"""
    provider = model_config.get("provider", "").lower()
    
    if provider == "dashscope":
        async for chunk in stream_dashscope_request(model_config, messages):
            if chunk.get("content"):
                yield chunk["content"]
    elif provider == "openai":
        async for chunk in stream_openai_request(model_config, messages):
            if chunk.get("content"):
                yield chunk["content"]
    elif provider == "anthropic":
        async for chunk in stream_anthropic_request(model_config, messages):
            if chunk.get("content"):
                yield chunk["content"]
    else:
        raise HTTPException(status_code=400, detail=f"不支持的模型提供商: {provider}")

async def stream_dashscope_request(model_config: Dict[str, Any], messages) -> AsyncGenerator[Dict[str, Any], None]:
    # 兼容两种消息格式：Dict 和 MessageRequest
    formatted_messages = []
    for msg in messages:
        if hasattr(msg, 'role'):  # MessageRequest对象
            formatted_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        else:  # Dict对象
            formatted_messages.append(msg)
    
    payload = {
        "model": model_config["model"],
        "input": {
            "messages": formatted_messages
        },
        "parameters": {
            "temperature": model_config["parameters"].get("temperature", 0.7),
            "max_tokens": model_config["parameters"].get("max_tokens", 2000),
            "top_p": model_config["parameters"].get("top_p", 0.8),
            "incremental_output": True,
            "result_format": "message"
        }
    }
    
    headers = {
        "Authorization": f"Bearer {model_config['apiKey']}",
        "Content-Type": "application/json",
        "Accept": "text/event-stream",
        "X-DashScope-SSE": "enable"
    }
    
    try:
        print(f"发送DashScope请求到: {model_config['apiEndpoint']}")
        print(f"请求payload: {json.dumps(payload, ensure_ascii=False)}")

        async with httpx.AsyncClient(timeout=60.0) as client:
            async with client.stream(
                "POST",
                model_config["apiEndpoint"],
                json=payload,
                headers=headers
            ) as response:
                print(f"DashScope响应状态码: {response.status_code}")
                if response.status_code != 200:
                    error_text = await response.aread()
                    yield {"error": f"API请求失败 ({response.status_code}): {error_text.decode()}"}
                    return

                line_count = 0
                content_received = ""
                async for line in response.aiter_lines():
                    line_count += 1
                    print(f"收到第{line_count}行: '{line}' (长度: {len(line)})")
                    content_received += line + "\n"

                    # 尝试不同的行格式
                    if line.startswith("data:") or line.startswith("data :"):
                        try:
                            if line.startswith("data:"):
                                data_str = line[5:].strip()
                            else:
                                data_str = line[6:].strip()

                            print(f"解析数据: '{data_str}'")
                            if data_str and data_str != "[DONE]":
                                data = json.loads(data_str)
                                print(f"解析后的JSON: {data}")
                                if "output" in data and "choices" in data["output"]:
                                    choices = data["output"]["choices"]
                                    if choices and len(choices) > 0:
                                        choice = choices[0]
                                        if "message" in choice and "content" in choice["message"]:
                                            content = choice["message"]["content"]
                                            if content:
                                                yield {"content": content}
                                        
                                        # 检查是否完成
                                        if choice.get("finish_reason") == "stop":
                                            break
                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            yield {"error": f"解析响应失败: {str(e)}"}

                # 如果没有收到任何流式数据，尝试解析完整响应
                if line_count == 0:
                    print("没有收到流式数据，尝试解析完整响应...")
                    try:
                        full_response = await response.aread()
                        print(f"完整响应: {full_response.decode()}")
                        response_data = json.loads(full_response.decode())
                        if "output" in response_data and "choices" in response_data["output"]:
                            choices = response_data["output"]["choices"]
                            if choices and len(choices) > 0:
                                content = choices[0]["message"]["content"]
                                if content:
                                    yield {"content": content}
                    except Exception as e:
                        print(f"解析完整响应失败: {e}")
                        yield {"error": f"解析完整响应失败: {str(e)}"}

    except Exception as e:
        yield {"error": f"请求失败: {str(e)}"}

async def stream_openai_request(model_config: Dict[str, Any], messages) -> AsyncGenerator[Dict[str, Any], None]:
    # 兼容两种消息格式：Dict 和 MessageRequest
    formatted_messages = []
    for msg in messages:
        if hasattr(msg, 'role'):  # MessageRequest对象
            formatted_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        else:  # Dict对象
            formatted_messages.append(msg)
    
    payload = {
        "model": model_config["model"],
        "messages": formatted_messages,
        "temperature": model_config["parameters"].get("temperature", 0.7),
        "max_tokens": model_config["parameters"].get("max_tokens", 2000),
        "top_p": model_config["parameters"].get("top_p", 0.8),
        "stream": True
    }
    
    headers = {
        "Authorization": f"Bearer {model_config['apiKey']}",
        "Content-Type": "application/json"
    }
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            async with client.stream(
                "POST",
                model_config["apiEndpoint"],
                json=payload,
                headers=headers
            ) as response:
                if response.status_code != 200:
                    error_text = await response.aread()
                    yield {"error": f"API请求失败 ({response.status_code}): {error_text.decode()}"}
                    return
                
                async for line in response.aiter_lines():
                    if line.startswith("data:"):
                        try:
                            data_str = line[5:].strip()
                            if data_str and data_str != "[DONE]":
                                data = json.loads(data_str)
                                if "choices" in data and len(data["choices"]) > 0:
                                    choice = data["choices"][0]
                                    if "delta" in choice and "content" in choice["delta"]:
                                        content = choice["delta"]["content"]
                                        if content:
                                            yield {"content": content}
                                    
                                    # 检查是否完成
                                    if choice.get("finish_reason") == "stop":
                                        break
                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            yield {"error": f"解析响应失败: {str(e)}"}
                            
    except Exception as e:
        yield {"error": f"请求失败: {str(e)}"}

async def stream_anthropic_request(model_config: Dict[str, Any], messages) -> AsyncGenerator[Dict[str, Any], None]:
    # 兼容两种消息格式：Dict 和 MessageRequest
    formatted_messages = []
    for msg in messages:
        if hasattr(msg, 'role'):  # MessageRequest对象
            formatted_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        else:  # Dict对象
            formatted_messages.append(msg)
    
    payload = {
        "model": model_config["model"],
        "messages": formatted_messages,
        "temperature": model_config["parameters"].get("temperature", 0.7),
        "max_tokens": model_config["parameters"].get("max_tokens", 2000),
        "stream": True
    }
    
    headers = {
        "x-api-key": model_config["apiKey"],
        "Content-Type": "application/json",
        "anthropic-version": "2023-06-01"
    }
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            async with client.stream(
                "POST",
                model_config["apiEndpoint"],
                json=payload,
                headers=headers
            ) as response:
                if response.status_code != 200:
                    error_text = await response.aread()
                    yield {"error": f"API请求失败 ({response.status_code}): {error_text.decode()}"}
                    return
                
                async for line in response.aiter_lines():
                    if line.startswith("data:"):
                        try:
                            data_str = line[5:].strip()
                            if data_str and data_str != "[DONE]":
                                data = json.loads(data_str)
                                if data.get("type") == "content_block_delta":
                                    if "delta" in data and "text" in data["delta"]:
                                        content = data["delta"]["text"]
                                        if content:
                                            yield {"content": content}
                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            yield {"error": f"解析响应失败: {str(e)}"}
                            
    except Exception as e:
        yield {"error": f"请求失败: {str(e)}"}

# 根据模型提供商分发非流式请求
async def get_chat_response(model_config: Dict[str, Any], messages) -> Dict[str, Any]:
    """根据模型提供商分发非流式请求"""
    provider = model_config.get("provider", "").lower()
    
    if provider == "dashscope":
        return await get_dashscope_response(model_config, messages)
    elif provider == "openai":
        return await get_openai_response(model_config, messages)
    elif provider == "anthropic":
        return await get_anthropic_response(model_config, messages)
    else:
        raise HTTPException(status_code=400, detail=f"不支持的模型提供商: {provider}")

async def get_dashscope_response(model_config: Dict[str, Any], messages) -> Dict[str, Any]:
    # 兼容两种消息格式：Dict 和 MessageRequest
    formatted_messages = []
    for msg in messages:
        if hasattr(msg, 'role'):  # MessageRequest对象
            formatted_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        else:  # Dict对象
            formatted_messages.append(msg)
    
    payload = {
        "model": model_config["model"],
        "input": {
            "messages": formatted_messages
        },
        "parameters": {
            "temperature": model_config["parameters"].get("temperature", 0.7),
            "max_tokens": model_config["parameters"].get("max_tokens", 2000),
            "top_p": model_config["parameters"].get("top_p", 0.8)
        }
    }
    
    headers = {
        "Authorization": f"Bearer {model_config['apiKey']}",
        "Content-Type": "application/json"
    }
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                model_config["apiEndpoint"],
                json=payload,
                headers=headers
            )
            
            if response.status_code != 200:
                raise HTTPException(status_code=response.status_code, detail=f"API请求失败: {response.text}")
            
            data = response.json()
            if "output" in data and "choices" in data["output"]:
                choices = data["output"]["choices"]
                if choices and len(choices) > 0:
                    content = choices[0]["message"]["content"]
                    usage = data.get("usage", {})
                    return {"content": content, "usage": usage}
            
            raise HTTPException(status_code=500, detail="API响应格式错误")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"请求失败: {str(e)}")

async def get_openai_response(model_config: Dict[str, Any], messages) -> Dict[str, Any]:
    # 兼容两种消息格式：Dict 和 MessageRequest
    formatted_messages = []
    for msg in messages:
        if hasattr(msg, 'role'):  # MessageRequest对象
            formatted_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        else:  # Dict对象
            formatted_messages.append(msg)
    
    payload = {
        "model": model_config["model"],
        "messages": formatted_messages,
        "temperature": model_config["parameters"].get("temperature", 0.7),
        "max_tokens": model_config["parameters"].get("max_tokens", 2000),
        "top_p": model_config["parameters"].get("top_p", 0.8)
    }
    
    headers = {
        "Authorization": f"Bearer {model_config['apiKey']}",
        "Content-Type": "application/json"
    }
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                model_config["apiEndpoint"],
                json=payload,
                headers=headers
            )
            
            if response.status_code != 200:
                raise HTTPException(status_code=response.status_code, detail=f"API请求失败: {response.text}")
            
            data = response.json()
            if "choices" in data and len(data["choices"]) > 0:
                content = data["choices"][0]["message"]["content"]
                usage = data.get("usage", {})
                return {"content": content, "usage": usage}
            
            raise HTTPException(status_code=500, detail="API响应格式错误")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"请求失败: {str(e)}")

async def get_anthropic_response(model_config: Dict[str, Any], messages) -> Dict[str, Any]:
    # 兼容两种消息格式：Dict 和 MessageRequest
    formatted_messages = []
    for msg in messages:
        if hasattr(msg, 'role'):  # MessageRequest对象
            formatted_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        else:  # Dict对象
            formatted_messages.append(msg)
    
    payload = {
        "model": model_config["model"],
        "messages": formatted_messages,
        "temperature": model_config["parameters"].get("temperature", 0.7),
        "max_tokens": model_config["parameters"].get("max_tokens", 2000)
    }
    
    headers = {
        "x-api-key": model_config["apiKey"],
        "Content-Type": "application/json",
        "anthropic-version": "2023-06-01"
    }
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                model_config["apiEndpoint"],
                json=payload,
                headers=headers
            )
            
            if response.status_code != 200:
                raise HTTPException(status_code=response.status_code, detail=f"API请求失败: {response.text}")
            
            data = response.json()
            if "content" in data and len(data["content"]) > 0:
                content = data["content"][0]["text"]
                usage = data.get("usage", {})
                return {"content": content, "usage": usage}
            
            raise HTTPException(status_code=500, detail="API响应格式错误")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"请求失败: {str(e)}") 