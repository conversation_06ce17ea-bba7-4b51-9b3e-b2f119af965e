#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的DashScope API测试
"""

import httpx
import json

def test_dashscope():
    """测试DashScope API"""
    print("开始测试DashScope API...")
    
    url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    headers = {
        "Authorization": "Bearer sk-4606dfde828a4f9aa7a43f5d53dddb9e",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "qwen-plus",
        "input": {
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "你好，请简单介绍一下自己。"}
            ]
        },
        "parameters": {
            "temperature": 0.7,
            "max_tokens": 200
        }
    }
    
    try:
        print("发送请求...")
        with httpx.Client(timeout=30.0) as client:
            response = client.post(url, json=payload, headers=headers)
            
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("API调用成功！")
            print(f"回复: {result['output']['choices'][0]['message']['content']}")
            return True
        else:
            print(f"API调用失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"请求失败: {e}")
        return False

if __name__ == "__main__":
    test_dashscope()
