package com.superwu.domain.shared.entity;

import com.superwu.domain.shared.valueobject.Id;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * DDD基础实体类
 * 提供ID、创建时间、更新时间等通用字段
 */
@Getter
@Setter
public abstract class BaseEntity {
    
    protected Id id;
    protected LocalDateTime createdAt;
    protected LocalDateTime updatedAt;
    
    protected BaseEntity() {
        this.id = Id.generate();
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    protected BaseEntity(Id id) {
        this.id = Objects.requireNonNull(id, "ID cannot be null");
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 检查实体是否为新实体（未持久化）
     */
    public boolean isNew() {
        return id == null;
    }
    
    /**
     * 更新实体的修改时间
     */
    public void touch() {
        this.updatedAt = LocalDateTime.now();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BaseEntity that = (BaseEntity) o;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return getClass().getSimpleName() + "{" +
                "id=" + id +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
} 