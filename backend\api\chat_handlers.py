from fastapi import HTTPException
from typing import AsyncGenerator, Dict, Any
import httpx
import json

# 根据模型提供商分发流式请求
async def stream_chat_response(model_config: Dict[str, Any], messages, rag_options=None, db=None, attachments=None) -> AsyncGenerator[str, None]:
    """根据模型提供商分发流式请求"""
    print("=" * 50)
    print("🔥 STREAM_CHAT_RESPONSE 函数被调用了！")
    print("=" * 50)

    provider = model_config.get("provider", "").lower()
    print(f"🔄 stream_chat_response: provider={provider}")
    print(f"🔄 model_config keys: {list(model_config.keys())}")
    print(f"🔄 messages: {messages}")

    if provider == "dashscope":
        print("📡 调用 stream_dashscope_request")
        chunk_count = 0
        try:
            async for chunk in stream_dashscope_request(model_config, messages, attachments):
                chunk_count += 1
                print(f"📦 收到chunk {chunk_count}: {chunk}")
                if chunk.get("content"):
                    print(f"✅ 返回内容: '{chunk['content']}'")
                    yield chunk["content"]
                else:
                    print(f"⚠️ chunk没有content字段: {chunk}")
            print(f"📊 DashScope总共返回 {chunk_count} 个chunks")
        except Exception as e:
            print(f"❌ stream_dashscope_request异常: {e}")
            import traceback
            traceback.print_exc()
    elif provider == "openai":
        async for chunk in stream_openai_request(model_config, messages):
            if chunk.get("content"):
                yield chunk["content"]
    elif provider == "anthropic":
        async for chunk in stream_anthropic_request(model_config, messages):
            if chunk.get("content"):
                yield chunk["content"]
    else:
        raise HTTPException(status_code=400, detail=f"不支持的模型提供商: {provider}")

async def stream_dashscope_request(model_config: Dict[str, Any], messages, attachments=None) -> AsyncGenerator[Dict[str, Any], None]:
    print("🚀 进入 stream_dashscope_request 函数")
    print(f"📋 model_config: {model_config['model']}")
    print(f"💬 messages count: {len(messages)}")
    print(f"📎 attachments count: {len(attachments) if attachments else 0}")

    # 兼容两种消息格式：Dict 和 MessageRequest，并支持多模态
    formatted_messages = []
    for i, msg in enumerate(messages):
        if hasattr(msg, 'role'):  # MessageRequest对象
            message_content = msg.content
            role = msg.role
        else:  # Dict对象
            message_content = msg.get("content", "")
            role = msg.get("role", "user")

        # 检查是否是最后一条用户消息且有附件
        # 找到最后一条用户消息
        last_user_message_index = -1
        for j in range(len(messages) - 1, -1, -1):
            check_msg = messages[j]
            check_role = check_msg.get("role", "user") if isinstance(check_msg, dict) else check_msg.role
            if check_role == "user":
                last_user_message_index = j
                break

        is_last_user_message = (i == last_user_message_index and role == "user")

        if is_last_user_message and attachments and len(attachments) > 0:
            # 构建多模态消息内容
            content_parts = []

            # 添加文本内容（如果有）
            if message_content.strip():
                content_parts.append({
                    "type": "text",
                    "text": message_content
                })

            # 添加图片内容
            for attachment in attachments:
                if attachment.get("type") == "image":
                    # 构造符合DashScope API要求的图片格式
                    base64_data = attachment.get("base64_data")
                    if base64_data:
                        # 如果base64数据不包含data URL前缀，则添加
                        if not base64_data.startswith("data:"):
                            # 假设是PNG格式，如果需要支持其他格式，可以从文件名或其他地方获取
                            base64_data = f"data:image/png;base64,{base64_data}"

                        content_parts.append({
                            "type": "image",
                            "image": base64_data
                        })

            formatted_messages.append({
                "role": role,
                "content": content_parts
            })
        else:
            # 普通文本消息
            formatted_messages.append({
                "role": role,
                "content": message_content
            })

    # 简化日志输出
    print(f"📝 formatted_messages: {len(formatted_messages)} messages prepared")
    
    payload = {
        "model": model_config["model"],
        "input": {
            "messages": formatted_messages
        },
        "parameters": {
            "temperature": model_config["parameters"].get("temperature", 0.7),
            "max_tokens": model_config["parameters"].get("max_tokens", 2000),
            "top_p": model_config["parameters"].get("top_p", 0.8),
            "incremental_output": True,
            "result_format": "message"
        }
    }
    
    headers = {
        "Authorization": f"Bearer {model_config['apiKey']}",
        "Content-Type": "application/json",
        "Accept": "text/event-stream",
        "X-DashScope-SSE": "enable"
    }
    
    try:
        print(f"🌐 发送DashScope请求到: {model_config['apiEndpoint']}")

        # 创建简化的payload用于日志（隐藏base64数据）
        payload_summary = payload.copy()
        if "input" in payload_summary and "messages" in payload_summary["input"]:
            messages_summary = []
            for msg in payload_summary["input"]["messages"]:
                if isinstance(msg.get("content"), list):
                    content_summary = []
                    for part in msg["content"]:
                        if part.get("type") == "image":
                            content_summary.append({"type": "image", "image": "[BASE64_DATA_HIDDEN]"})
                        else:
                            content_summary.append(part)
                    messages_summary.append({"role": msg["role"], "content": content_summary})
                else:
                    messages_summary.append(msg)
            payload_summary["input"]["messages"] = messages_summary

        print(f"📤 请求payload大小: {len(json.dumps(payload))} 字符")
        print(f"📋 请求headers: Authorization=[HIDDEN], Content-Type={headers['Content-Type']}")

        async with httpx.AsyncClient(timeout=60.0) as client:
            print("🔗 创建HTTP客户端成功")
            async with client.stream(
                "POST",
                model_config["apiEndpoint"],
                json=payload,
                headers=headers
            ) as response:
                print("📡 开始流式请求")
                print(f"DashScope响应状态码: {response.status_code}")
                print(f"响应头: {dict(response.headers)}")

                if response.status_code != 200:
                    error_text = await response.aread()
                    yield {"error": f"API请求失败 ({response.status_code}): {error_text.decode()}"}
                    return

                # 检查响应是否真的是流式的
                content_type = response.headers.get("content-type", "")
                print(f"Content-Type: {content_type}")

                line_count = 0
                content_received = ""
                print("开始读取流式响应...")

                async for line in response.aiter_lines():
                    line_count += 1
                    print(f"收到第{line_count}行: '{line}' (长度: {len(line)})")
                    content_received += line + "\n"

                    # 只处理以data:开头的行
                    if line.startswith("data:"):
                        try:
                            data_str = line[5:].strip()
                            print(f"解析数据: '{data_str}'")

                            if data_str and data_str != "[DONE]":
                                data = json.loads(data_str)
                                print(f"解析后的JSON: {json.dumps(data, ensure_ascii=False)}")

                                if "output" in data and "choices" in data["output"]:
                                    choices = data["output"]["choices"]
                                    if choices and len(choices) > 0:
                                        choice = choices[0]
                                        if "message" in choice and "content" in choice["message"]:
                                            content = choice["message"]["content"]
                                            if content:  # 只有非空内容才yield
                                                print(f"提取到内容: '{content}'")

                                                # 处理不同的内容格式
                                                if isinstance(content, list):
                                                    # 如果是列表格式，提取文本内容
                                                    text_content = ""
                                                    for item in content:
                                                        if isinstance(item, dict) and "text" in item:
                                                            text_content += item["text"]
                                                    if text_content:
                                                        yield {"content": text_content}
                                                elif isinstance(content, str):
                                                    # 如果是字符串格式，直接返回
                                                    yield {"content": content}

                                        # 检查是否完成
                                        if choice.get("finish_reason") == "stop":
                                            print("收到完成信号，结束流式响应")
                                            break
                        except json.JSONDecodeError as e:
                            print(f"JSON解析失败: {e}")
                            continue
                        except Exception as e:
                            print(f"解析响应失败: {e}")
                            yield {"error": f"解析响应失败: {str(e)}"}

                print(f"流式响应循环结束，总共收到 {line_count} 行")

    except Exception as e:
        print(f"❌ stream_dashscope_request异常: {e}")
        import traceback
        traceback.print_exc()
        yield {"error": f"请求失败: {str(e)}"}

async def stream_openai_request(model_config: Dict[str, Any], messages) -> AsyncGenerator[Dict[str, Any], None]:
    # 兼容两种消息格式：Dict 和 MessageRequest
    formatted_messages = []
    for msg in messages:
        if hasattr(msg, 'role'):  # MessageRequest对象
            formatted_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        else:  # Dict对象
            formatted_messages.append(msg)
    
    payload = {
        "model": model_config["model"],
        "messages": formatted_messages,
        "temperature": model_config["parameters"].get("temperature", 0.7),
        "max_tokens": model_config["parameters"].get("max_tokens", 2000),
        "top_p": model_config["parameters"].get("top_p", 0.8),
        "stream": True
    }
    
    headers = {
        "Authorization": f"Bearer {model_config['apiKey']}",
        "Content-Type": "application/json"
    }
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            async with client.stream(
                "POST",
                model_config["apiEndpoint"],
                json=payload,
                headers=headers
            ) as response:
                if response.status_code != 200:
                    error_text = await response.aread()
                    yield {"error": f"API请求失败 ({response.status_code}): {error_text.decode()}"}
                    return
                
                async for line in response.aiter_lines():
                    if line.startswith("data:"):
                        try:
                            data_str = line[5:].strip()
                            if data_str and data_str != "[DONE]":
                                data = json.loads(data_str)
                                if "choices" in data and len(data["choices"]) > 0:
                                    choice = data["choices"][0]
                                    if "delta" in choice and "content" in choice["delta"]:
                                        content = choice["delta"]["content"]
                                        if content:
                                            yield {"content": content}
                                    
                                    # 检查是否完成
                                    if choice.get("finish_reason") == "stop":
                                        break
                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            yield {"error": f"解析响应失败: {str(e)}"}
                            
    except Exception as e:
        yield {"error": f"请求失败: {str(e)}"}

async def stream_anthropic_request(model_config: Dict[str, Any], messages) -> AsyncGenerator[Dict[str, Any], None]:
    # 兼容两种消息格式：Dict 和 MessageRequest
    formatted_messages = []
    for msg in messages:
        if hasattr(msg, 'role'):  # MessageRequest对象
            formatted_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        else:  # Dict对象
            formatted_messages.append(msg)
    
    payload = {
        "model": model_config["model"],
        "messages": formatted_messages,
        "temperature": model_config["parameters"].get("temperature", 0.7),
        "max_tokens": model_config["parameters"].get("max_tokens", 2000),
        "stream": True
    }
    
    headers = {
        "x-api-key": model_config["apiKey"],
        "Content-Type": "application/json",
        "anthropic-version": "2023-06-01"
    }
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            async with client.stream(
                "POST",
                model_config["apiEndpoint"],
                json=payload,
                headers=headers
            ) as response:
                if response.status_code != 200:
                    error_text = await response.aread()
                    yield {"error": f"API请求失败 ({response.status_code}): {error_text.decode()}"}
                    return
                
                async for line in response.aiter_lines():
                    if line.startswith("data:"):
                        try:
                            data_str = line[5:].strip()
                            if data_str and data_str != "[DONE]":
                                data = json.loads(data_str)
                                if data.get("type") == "content_block_delta":
                                    if "delta" in data and "text" in data["delta"]:
                                        content = data["delta"]["text"]
                                        if content:
                                            yield {"content": content}
                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            yield {"error": f"解析响应失败: {str(e)}"}
                            
    except Exception as e:
        yield {"error": f"请求失败: {str(e)}"}

# 根据模型提供商分发非流式请求
async def get_chat_response(model_config: Dict[str, Any], messages) -> Dict[str, Any]:
    """根据模型提供商分发非流式请求"""
    provider = model_config.get("provider", "").lower()
    
    if provider == "dashscope":
        return await get_dashscope_response(model_config, messages)
    elif provider == "openai":
        return await get_openai_response(model_config, messages)
    elif provider == "anthropic":
        return await get_anthropic_response(model_config, messages)
    else:
        raise HTTPException(status_code=400, detail=f"不支持的模型提供商: {provider}")

async def get_dashscope_response(model_config: Dict[str, Any], messages) -> Dict[str, Any]:
    # 兼容两种消息格式：Dict 和 MessageRequest
    formatted_messages = []
    for msg in messages:
        if hasattr(msg, 'role'):  # MessageRequest对象
            formatted_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        else:  # Dict对象
            formatted_messages.append(msg)
    
    payload = {
        "model": model_config["model"],
        "input": {
            "messages": formatted_messages
        },
        "parameters": {
            "temperature": model_config["parameters"].get("temperature", 0.7),
            "max_tokens": model_config["parameters"].get("max_tokens", 2000),
            "top_p": model_config["parameters"].get("top_p", 0.8)
        }
    }
    
    headers = {
        "Authorization": f"Bearer {model_config['apiKey']}",
        "Content-Type": "application/json"
    }
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                model_config["apiEndpoint"],
                json=payload,
                headers=headers
            )
            
            if response.status_code != 200:
                raise HTTPException(status_code=response.status_code, detail=f"API请求失败: {response.text}")
            
            data = response.json()
            if "output" in data and "choices" in data["output"]:
                choices = data["output"]["choices"]
                if choices and len(choices) > 0:
                    content = choices[0]["message"]["content"]
                    usage = data.get("usage", {})
                    return {"content": content, "usage": usage}
            
            raise HTTPException(status_code=500, detail="API响应格式错误")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"请求失败: {str(e)}")

async def get_openai_response(model_config: Dict[str, Any], messages) -> Dict[str, Any]:
    # 兼容两种消息格式：Dict 和 MessageRequest
    formatted_messages = []
    for msg in messages:
        if hasattr(msg, 'role'):  # MessageRequest对象
            formatted_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        else:  # Dict对象
            formatted_messages.append(msg)
    
    payload = {
        "model": model_config["model"],
        "messages": formatted_messages,
        "temperature": model_config["parameters"].get("temperature", 0.7),
        "max_tokens": model_config["parameters"].get("max_tokens", 2000),
        "top_p": model_config["parameters"].get("top_p", 0.8)
    }
    
    headers = {
        "Authorization": f"Bearer {model_config['apiKey']}",
        "Content-Type": "application/json"
    }
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                model_config["apiEndpoint"],
                json=payload,
                headers=headers
            )
            
            if response.status_code != 200:
                raise HTTPException(status_code=response.status_code, detail=f"API请求失败: {response.text}")
            
            data = response.json()
            if "choices" in data and len(data["choices"]) > 0:
                content = data["choices"][0]["message"]["content"]
                usage = data.get("usage", {})
                return {"content": content, "usage": usage}
            
            raise HTTPException(status_code=500, detail="API响应格式错误")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"请求失败: {str(e)}")

async def get_anthropic_response(model_config: Dict[str, Any], messages) -> Dict[str, Any]:
    # 兼容两种消息格式：Dict 和 MessageRequest
    formatted_messages = []
    for msg in messages:
        if hasattr(msg, 'role'):  # MessageRequest对象
            formatted_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        else:  # Dict对象
            formatted_messages.append(msg)
    
    payload = {
        "model": model_config["model"],
        "messages": formatted_messages,
        "temperature": model_config["parameters"].get("temperature", 0.7),
        "max_tokens": model_config["parameters"].get("max_tokens", 2000)
    }
    
    headers = {
        "x-api-key": model_config["apiKey"],
        "Content-Type": "application/json",
        "anthropic-version": "2023-06-01"
    }
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                model_config["apiEndpoint"],
                json=payload,
                headers=headers
            )
            
            if response.status_code != 200:
                raise HTTPException(status_code=response.status_code, detail=f"API请求失败: {response.text}")
            
            data = response.json()
            if "content" in data and len(data["content"]) > 0:
                content = data["content"][0]["text"]
                usage = data.get("usage", {})
                return {"content": content, "usage": usage}
            
            raise HTTPException(status_code=500, detail="API响应格式错误")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"请求失败: {str(e)}") 