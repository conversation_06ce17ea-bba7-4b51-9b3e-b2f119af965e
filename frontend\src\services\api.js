import axios from 'axios'

const API_BASE_URL = '/api'

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
})

// 响应拦截器
api.interceptors.response.use(
  (response) => response.data,
  (error) => {
    console.error('API错误:', error)
    throw error
  }
)

export default {
  // 模型管理
  async getModels() {
    return api.get('/models')
  },
  
  async addModel(modelConfig) {
    return api.post('/models', modelConfig)
  },
  
  async updateModel(id, modelConfig) {
    return api.put(`/models/${id}`, modelConfig)
  },
  
  async deleteModel(id) {
    return api.delete(`/models/${id}`)
  },
  
  // 聊天功能
  async sendMessage(modelId, messages) {
    return api.post('/chat', {
      modelId,
      messages
    })
  },

  // 流式聊天功能
  async sendMessageStream(modelId, messages, conversationId, onChunk, onComplete, onError) {
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          modelId,
          messages,
          conversationId, // 添加对话ID
          stream: true
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          onComplete?.();
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue;
          
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim();
            
            if (data === '[DONE]') {
              onComplete?.();
              return;
            }

            try {
              const parsed = JSON.parse(data);
              if (parsed.error) {
                onError?.(new Error(parsed.error));
                return;
              }
              onChunk?.(parsed);
            } catch (e) {
              console.error('解析流式数据错误:', e, data);
            }
          }
        }
      }
    } catch (error) {
      onError?.(error);
    }
  },

  // 对话管理API
  async getConversations(limit = 50, offset = 0) {
    const response = await fetch(`${API_BASE_URL}/api/conversations?limit=${limit}&offset=${offset}`);
    
    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status}`);
    }
    
    return response.json();
  },

  async createConversation(title = '新对话', modelId = null, systemPrompt = null) {
    const response = await fetch(`${API_BASE_URL}/api/conversations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        title,
        model_id: modelId,
        system_prompt: systemPrompt
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status}`);
    }

    return response.json();
  },

  async getConversation(conversationId) {
    const response = await fetch(`${API_BASE_URL}/api/conversations/${conversationId}`);
    
    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status}`);
    }
    
    return response.json();
  },

  async deleteConversation(conversationId) {
    const response = await fetch(`${API_BASE_URL}/api/conversations/${conversationId}`, {
      method: 'DELETE'
    });

    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status}`);
    }

    return response.json();
  },

  // 获取聊天统计信息
  async getChatStatistics() {
    const response = await fetch(`${API_BASE_URL}/api/statistics`);
    
    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status}`);
    }
    
    return response.json();
  },

  // 测试模型
  async testModel(modelId, input) {
    // Implementation needed
  },

  // 导出数据
  async exportData(modelId) {
    // Implementation needed
  },

  // 导入数据
  async importData(modelId, data) {
    // Implementation needed
  }
}

// 知识库管理API
export const knowledgeApi = {
  // 获取知识库列表
  async getRepositories() {
    return api.get('/knowledge/repositories')
  },

  // 创建知识库
  async createRepository(data) {
    return api.post('/knowledge/repositories', data)
  },

  // 获取知识库详情
  async getRepository(id) {
    return api.get(`/knowledge/repositories/${id}`)
  },

  // 更新知识库
  async updateRepository(id, data) {
    return api.put(`/knowledge/repositories/${id}`, data)
  },

  // 删除知识库
  async deleteRepository(id) {
    return api.delete(`/knowledge/repositories/${id}`)
  },

  // 获取知识库文档列表
  async getDocuments(repositoryId) {
    return api.get(`/knowledge/repositories/${repositoryId}/documents`)
  },

  // 上传文档
  async uploadDocument(repositoryId, file) {
    const formData = new FormData()
    formData.append('file', file)
    
    return api.post(`/knowledge/repositories/${repositoryId}/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取文档详情
  async getDocument(id) {
    return api.get(`/knowledge/documents/${id}`)
  },

  // 删除文档
  async deleteDocument(id) {
    return api.delete(`/knowledge/documents/${id}`)
  },

  // 知识搜索
  async searchKnowledge(searchData) {
    return api.post('/knowledge/search', searchData)
  },

  // RAG聊天
  async ragChatStream(ragData, onChunk, onComplete, onError) {
    try {
      // 转换参数格式以匹配后端期望的RAGChatRequest
      const requestBody = {
        modelId: ragData.model_id,
        messages: ragData.messages || [{ role: 'user', content: ragData.query }],
        repositoryId: ragData.repository_id,
        stream: true,
        conversationId: ragData.conversation_id || null,
        ragTopK: ragData.top_k || 5,
        ragThreshold: ragData.threshold || 0.7
      };

      const response = await fetch('/api/rag/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          onComplete?.();
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.trim() === '') continue;
          
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim();
            
            if (data === '[DONE]') {
              onComplete?.();
              return;
            }

            try {
              const parsed = JSON.parse(data);
              if (parsed.error) {
                onError?.(new Error(parsed.error));
                return;
              }
              onChunk?.(parsed);
            } catch (e) {
              console.error('解析RAG流式数据错误:', e, data);
            }
          }
        }
      }
    } catch (error) {
      onError?.(error);
    }
  }
} 