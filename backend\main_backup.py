from fastapi import FastAPI, HTTPException, Depends, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Any, AsyncGenerator
import httpx
import json
import asyncio
import os
import shutil
from pathlib import Path
from dotenv import load_dotenv
import uuid
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_

# 导入数据库和上下文管理
from database import get_db, create_tables, SessionLocal, MetadataHandler, ContentHandler
from database import User as DBUser, ModelConfig as DBModelConfig, Conversation as DBConversation, Message as DBMessage, ChatStatistic as DBChatStatistic
from database import KnowledgeRepository as DBKnowledgeRepository, Document as DBDocument, DocumentChunk as DBDocumentChunk, SearchHistory as DBSearchHistory
from chat_context import ContextManager, ChatMessage, ChatContext

# 导入RAG功能
from rag_core import rag_processor

load_dotenv()

# 创建数据库表
create_tables()

# 默认用户ID (可以通过认证系统获取)
DEFAULT_USER_ID = "550e8400-e29b-41d4-a716-************"

# 初始化默认数据
def init_default_data():
    """初始化默认用户和模型配置"""
    db = SessionLocal()
    try:
        # 检查是否已有用户
        existing_user = db.query(DBUser).filter(DBUser.id == DEFAULT_USER_ID).first()
        if not existing_user:
            # 创建默认用户
            default_user = DBUser(
                id=DEFAULT_USER_ID,
                username="默认用户",
                email="<EMAIL>"
            )
            db.add(default_user)
            print(f"创建默认用户: {DEFAULT_USER_ID}")
        
        # 检查是否已有默认模型
        existing_model = db.query(DBModelConfig).filter(DBModelConfig.id == "550e8400-e29b-41d4-a716-************").first()
        if not existing_model:
            # 创建默认通义千问模型配置
            default_model = DBModelConfig(
                id="550e8400-e29b-41d4-a716-************",
                name="通义千问",
                provider="dashscope",
                api_endpoint="https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",
                api_key_encrypted="your_api_key_here",  # 请用户自己配置
                model="qwen-turbo",
                enabled=True,
                parameters={
                    "temperature": 0.7,
                    "max_tokens": 2000,
                    "top_p": 0.8
                },
                created_by=DEFAULT_USER_ID
            )
            db.add(default_model)
            print(f"创建默认模型配置: 通义千问")
        
        db.commit()
        print("✅ 默认数据初始化完成")
        
    except Exception as e:
        print(f"❌ 初始化默认数据失败: {e}")
        db.rollback()
    finally:
        db.close()

# 启动时初始化默认数据
init_default_data()

app = FastAPI(title="🤖 多模型聊天机器人API", version="2.0.0", description="支持多AI模型的智能聊天机器人后端API")

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据模型
class MessageRequest(BaseModel):
    role: str
    content: str

class ModelParameters(BaseModel):
    temperature: float = 0.7
    max_tokens: int = 2000
    top_p: Optional[float] = 0.8

class ModelConfigRequest(BaseModel):
    id: Optional[str] = None
    name: str
    provider: str
    apiEndpoint: str
    apiKey: str
    model: str
    enabled: bool = True
    parameters: ModelParameters

class ChatRequest(BaseModel):
    modelId: str
    messages: List[MessageRequest]
    stream: bool = True
    conversationId: Optional[str] = None
    systemPrompt: Optional[str] = None
    title: Optional[str] = None

class ChatResponse(BaseModel):
    content: str
    model: str
    usage: Optional[Dict[str, Any]] = None

# RAG相关数据模型
class KnowledgeRepositoryRequest(BaseModel):
    name: str
    description: Optional[str] = None
    is_public: bool = False
    chunk_size: int = 1000
    chunk_overlap: int = 200

class KnowledgeRepositoryResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    is_public: bool
    document_count: int
    total_chunks: int
    embedding_model: str
    chunk_size: int
    chunk_overlap: int
    created_at: datetime
    updated_at: datetime

class DocumentResponse(BaseModel):
    id: str
    filename: str
    original_filename: str
    file_type: str
    file_size: int
    title: Optional[str]
    author: Optional[str]
    chunk_count: int
    processing_status: str
    processing_error: Optional[str]
    created_at: datetime

class SearchRequest(BaseModel):
    query: str
    repository_id: str
    top_k: int = 5
    threshold: float = 0.7

class SearchResult(BaseModel):
    chunk_id: str
    document_id: str
    content: str
    relevance_score: float
    document_title: str
    document_filename: str
    chunk_index: int

class RAGChatRequest(BaseModel):
    modelId: str
    messages: List[MessageRequest]
    repositoryId: str
    stream: bool = True
    conversationId: Optional[str] = None
    systemPrompt: Optional[str] = None
    title: Optional[str] = None
    ragTopK: int = 5
    ragThreshold: float = 0.7

# 健康检查
@app.get("/health")
async def health_check():
    return {"status": "ok", "message": "服务运行正常"}

# 获取消息元数据统计
@app.get("/api/messages/{message_id}/metadata")
async def get_message_metadata(message_id: str, db: Session = Depends(get_db)):
    """获取消息的元数据信息"""
    message = db.query(DBMessage).filter(DBMessage.id == message_id).first()
    if not message:
        raise HTTPException(status_code=404, detail="消息未找到")
    
    # 提取原始元数据
    original_metadata = MetadataHandler.extract_metadata(message.msg_metadata or {})
    
    # 获取元数据统计
    metadata_stats = MetadataHandler.get_metadata_stats(message.msg_metadata or {})
    
    return {
        "message_id": str(message.id),
        "role": message.role,
        "content_length": len(message.content),
        "metadata": original_metadata,
        "metadata_stats": metadata_stats,
        "created_at": message.created_at
    }

# 获取对话的元数据统计
@app.get("/api/conversations/{conversation_id}/metadata-stats")
async def get_conversation_metadata_stats(conversation_id: str, db: Session = Depends(get_db)):
    """获取对话中所有消息的元数据统计"""
    messages = db.query(DBMessage).filter(DBMessage.conversation_id == conversation_id).all()
    if not messages:
        raise HTTPException(status_code=404, detail="对话或消息未找到")
    
    stats = {
        "total_messages": len(messages),
        "compressed_messages": 0,
        "total_metadata_size": 0,
        "total_original_size": 0,
        "average_compression_ratio": 0,
        "messages_by_role": {"user": 0, "assistant": 0, "system": 0}
    }
    
    compression_ratios = []
    
    for message in messages:
        stats["messages_by_role"][message.role] = stats["messages_by_role"].get(message.role, 0) + 1
        
        if message.msg_metadata:
            metadata_stats = MetadataHandler.get_metadata_stats(message.msg_metadata)
            
            if metadata_stats["compressed"]:
                stats["compressed_messages"] += 1
                if metadata_stats.get("compression_ratio"):
                    compression_ratios.append(metadata_stats["compression_ratio"])
            
            stats["total_metadata_size"] += metadata_stats.get("size", 0)
            if metadata_stats.get("original_size"):
                stats["total_original_size"] += metadata_stats["original_size"]
    
    if compression_ratios:
        stats["average_compression_ratio"] = sum(compression_ratios) / len(compression_ratios)
    
    stats["compression_savings"] = stats["total_original_size"] - stats["total_metadata_size"]
    stats["compression_percentage"] = (
        (stats["compression_savings"] / stats["total_original_size"] * 100) 
        if stats["total_original_size"] > 0 else 0
    )
    
    return stats

# 获取消息内容信息
@app.get("/api/messages/{message_id}/content-info")
async def get_message_content_info(message_id: str, db: Session = Depends(get_db)):
    """获取消息的内容处理信息"""
    message = db.query(DBMessage).filter(DBMessage.id == message_id).first()
    if not message:
        raise HTTPException(status_code=404, detail="消息未找到")
    
    return ContentHandler.get_content_info(message)

# ==================== RAG相关API ====================

# 获取知识仓库列表
@app.get("/api/knowledge/repositories", response_model=List[KnowledgeRepositoryResponse])
async def get_knowledge_repositories(
    skip: int = 0, 
    limit: int = 50,
    public_only: bool = False,
    db: Session = Depends(get_db)
):
    """获取知识仓库列表"""
    query = db.query(DBKnowledgeRepository)
    
    if public_only:
        query = query.filter(DBKnowledgeRepository.is_public == True)
    else:
        # 只显示当前用户的仓库或公开仓库
        query = query.filter(
            (DBKnowledgeRepository.owner_id == DEFAULT_USER_ID) | 
            (DBKnowledgeRepository.is_public == True)
        )
    
    repositories = query.offset(skip).limit(limit).all()
    
    return [KnowledgeRepositoryResponse(
        id=str(repo.id),
        name=repo.name,
        description=repo.description,
        is_public=repo.is_public,
        document_count=repo.document_count,
        total_chunks=repo.total_chunks,
        embedding_model=repo.embedding_model,
        chunk_size=repo.chunk_size,
        chunk_overlap=repo.chunk_overlap,
        created_at=repo.created_at,
        updated_at=repo.updated_at
    ) for repo in repositories]

# 创建知识仓库
@app.post("/api/knowledge/repositories", response_model=KnowledgeRepositoryResponse)
async def create_knowledge_repository(
    request: KnowledgeRepositoryRequest,
    db: Session = Depends(get_db)
):
    """创建新的知识仓库"""
    
    # 检查仓库名称是否已存在
    existing = db.query(DBKnowledgeRepository).filter(
        DBKnowledgeRepository.name == request.name,
        DBKnowledgeRepository.owner_id == DEFAULT_USER_ID
    ).first()
    
    if existing:
        raise HTTPException(status_code=400, detail="仓库名称已存在")
    
    repository = DBKnowledgeRepository(
        name=request.name,
        description=request.description,
        owner_id=DEFAULT_USER_ID,
        is_public=request.is_public,
        chunk_size=request.chunk_size,
        chunk_overlap=request.chunk_overlap
    )
    
    db.add(repository)
    db.commit()
    db.refresh(repository)
    
    return KnowledgeRepositoryResponse(
        id=str(repository.id),
        name=repository.name,
        description=repository.description,
        is_public=repository.is_public,
        document_count=repository.document_count,
        total_chunks=repository.total_chunks,
        embedding_model=repository.embedding_model,
        chunk_size=repository.chunk_size,
        chunk_overlap=repository.chunk_overlap,
        created_at=repository.created_at,
        updated_at=repository.updated_at
    )

# 获取单个知识仓库详情
@app.get("/api/knowledge/repositories/{repository_id}", response_model=KnowledgeRepositoryResponse)
async def get_knowledge_repository(repository_id: str, db: Session = Depends(get_db)):
    """获取知识仓库详情"""
    repository = db.query(DBKnowledgeRepository).filter(
        DBKnowledgeRepository.id == repository_id
    ).first()
    
    if not repository:
        raise HTTPException(status_code=404, detail="知识仓库未找到")
    
    return KnowledgeRepositoryResponse(
        id=str(repository.id),
        name=repository.name,
        description=repository.description,
        is_public=repository.is_public,
        document_count=repository.document_count,
        total_chunks=repository.total_chunks,
        embedding_model=repository.embedding_model,
        chunk_size=repository.chunk_size,
        chunk_overlap=repository.chunk_overlap,
        created_at=repository.created_at,
        updated_at=repository.updated_at
    )

# 更新知识仓库
@app.put("/api/knowledge/repositories/{repository_id}", response_model=KnowledgeRepositoryResponse)
async def update_knowledge_repository(
    repository_id: str,
    request: KnowledgeRepositoryRequest,
    db: Session = Depends(get_db)
):
    """更新知识仓库"""
    repository = db.query(DBKnowledgeRepository).filter(
        DBKnowledgeRepository.id == repository_id,
        DBKnowledgeRepository.owner_id == DEFAULT_USER_ID
    ).first()
    
    if not repository:
        raise HTTPException(status_code=404, detail="知识仓库未找到或无权限")
    
    repository.name = request.name
    repository.description = request.description
    repository.is_public = request.is_public
    repository.chunk_size = request.chunk_size
    repository.chunk_overlap = request.chunk_overlap
    
    db.commit()
    db.refresh(repository)
    
    return KnowledgeRepositoryResponse(
        id=str(repository.id),
        name=repository.name,
        description=repository.description,
        is_public=repository.is_public,
        document_count=repository.document_count,
        total_chunks=repository.total_chunks,
        embedding_model=repository.embedding_model,
        chunk_size=repository.chunk_size,
        chunk_overlap=repository.chunk_overlap,
        created_at=repository.created_at,
        updated_at=repository.updated_at
    )

# 删除知识仓库
@app.delete("/api/knowledge/repositories/{repository_id}")
async def delete_knowledge_repository(repository_id: str, db: Session = Depends(get_db)):
    """删除知识仓库"""
    repository = db.query(DBKnowledgeRepository).filter(
        DBKnowledgeRepository.id == repository_id,
        DBKnowledgeRepository.owner_id == DEFAULT_USER_ID
    ).first()
    
    if not repository:
        raise HTTPException(status_code=404, detail="知识仓库未找到或无权限")
    
    db.delete(repository)
    db.commit()
    
    return {"success": True, "message": "知识仓库已删除"}

# 文档上传
@app.post("/api/knowledge/repositories/{repository_id}/upload")
async def upload_document(
    repository_id: str,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """上传文档到知识仓库"""
    
    # 验证仓库是否存在
    repository = db.query(DBKnowledgeRepository).filter(
        DBKnowledgeRepository.id == repository_id
    ).first()
    
    if not repository:
        raise HTTPException(status_code=404, detail="知识仓库未找到")
    
    # 验证文件类型
    allowed_extensions = {'.pdf', '.docx', '.doc', '.txt', '.md', '.markdown'}
    file_extension = Path(file.filename).suffix.lower()
    
    if file_extension not in allowed_extensions:
        raise HTTPException(
            status_code=400, 
            detail=f"不支持的文件类型: {file_extension}。支持的类型: {', '.join(allowed_extensions)}"
        )
    
    # 检查文件大小 (最大50MB)
    max_file_size = 50 * 1024 * 1024  # 50MB
    if file.size and file.size > max_file_size:
        raise HTTPException(status_code=400, detail="文件大小不能超过50MB")
    
    try:
        # 创建上传目录
        upload_dir = Path("uploads") / str(repository_id)
        upload_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成唯一文件名
        unique_filename = f"{uuid.uuid4()}_{file.filename}"
        file_path = upload_dir / unique_filename
        
        # 保存文件
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # 异步处理文档
        document = await rag_processor.process_document(
            str(file_path),
            repository_id,
            file.filename,
            DEFAULT_USER_ID,
            db
        )
        
        return {
            "success": True,
            "message": "文档上传成功",
            "document_id": str(document.id),
            "filename": document.filename,
            "chunks_created": document.chunk_count
        }
        
    except Exception as e:
        # 清理上传的文件
        if 'file_path' in locals() and file_path.exists():
            file_path.unlink()
        
        raise HTTPException(status_code=500, detail=f"文档处理失败: {str(e)}")

# 获取仓库中的文档列表
@app.get("/api/knowledge/repositories/{repository_id}/documents", response_model=List[DocumentResponse])
async def get_repository_documents(
    repository_id: str,
    skip: int = 0,
    limit: int = 50,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """获取知识仓库中的文档列表"""
    
    query = db.query(DBDocument).filter(DBDocument.repository_id == repository_id)
    
    if status:
        query = query.filter(DBDocument.processing_status == status)
    
    documents = query.offset(skip).limit(limit).all()
    
    return [DocumentResponse(
        id=str(doc.id),
        filename=doc.filename,
        original_filename=doc.original_filename,
        file_type=doc.file_type,
        file_size=doc.file_size,
        title=doc.title,
        author=doc.author,
        chunk_count=doc.chunk_count,
        processing_status=doc.processing_status,
        processing_error=doc.processing_error,
        created_at=doc.created_at
    ) for doc in documents]

# 获取文档详情
@app.get("/api/knowledge/documents/{document_id}", response_model=DocumentResponse)
async def get_document(document_id: str, db: Session = Depends(get_db)):
    """获取文档详情"""
    document = db.query(DBDocument).filter(DBDocument.id == document_id).first()
    
    if not document:
        raise HTTPException(status_code=404, detail="文档未找到")
    
    return DocumentResponse(
        id=str(document.id),
        filename=document.filename,
        original_filename=document.original_filename,
        file_type=document.file_type,
        file_size=document.file_size,
        title=document.title,
        author=document.author,
        chunk_count=document.chunk_count,
        processing_status=document.processing_status,
        processing_error=document.processing_error,
        created_at=document.created_at
    )

# 删除文档
@app.delete("/api/knowledge/documents/{document_id}")
async def delete_document(document_id: str, db: Session = Depends(get_db)):
    """删除文档"""
    document = db.query(DBDocument).filter(DBDocument.id == document_id).first()
    
    if not document:
        raise HTTPException(status_code=404, detail="文档未找到")
    
    # 删除文件
    try:
        if Path(document.file_path).exists():
            Path(document.file_path).unlink()
    except Exception as e:
        print(f"删除文件失败: {e}")
    
    # 更新仓库统计
    repository = document.repository
    repository.document_count = max(0, repository.document_count - 1)
    repository.total_chunks = max(0, repository.total_chunks - document.chunk_count)
    
    # 删除数据库记录
    db.delete(document)
    db.commit()
    
    return {"success": True, "message": "文档已删除"}

# 知识检索
@app.post("/api/knowledge/search", response_model=List[SearchResult])
async def search_knowledge(request: SearchRequest, db: Session = Depends(get_db)):
    """在知识库中搜索相关内容"""
    
    if not request.query.strip():
        raise HTTPException(status_code=400, detail="搜索查询不能为空")
    
    try:
        results = await rag_processor.search_knowledge(
            query=request.query,
            repository_id=request.repository_id,
            top_k=request.top_k,
            threshold=request.threshold,
            user_id=DEFAULT_USER_ID,
            db=db
        )
        
        return [SearchResult(
            chunk_id=result["chunk_id"],
            document_id=result["document_id"],
            content=result["content"],
            relevance_score=result["relevance_score"],
            document_title=result["document_title"],
            document_filename=result["document_filename"],
            chunk_index=result["chunk_index"]
        ) for result in results]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

# RAG对话
@app.post("/api/rag/chat")
async def rag_chat(request: RAGChatRequest, db: Session = Depends(get_db)):
    """基于知识库的对话"""
    
    # 获取用户最新消息
    user_message = request.messages[-1].content if request.messages else ""
    if not user_message.strip():
        raise HTTPException(status_code=400, detail="消息内容不能为空")
    
    try:
        # 1. 检索相关知识
        search_results = await rag_processor.search_knowledge(
            query=user_message,
            repository_id=request.repositoryId,
            top_k=request.ragTopK,
            threshold=request.ragThreshold,
            user_id=DEFAULT_USER_ID,
            db=db
        )
        
        # 2. 构建增强的上下文
        context_content = ""
        if search_results:
            context_content = "\n\n=== 相关知识背景 ===\n"
            for i, result in enumerate(search_results, 1):
                context_content += f"\n[参考资料{i}] (来源: {result['document_filename']})\n"
                context_content += f"{result['content']}\n"
        
        # 3. 构建系统提示词
        system_prompt = request.systemPrompt or "你是一个有用的AI助手。"
        if context_content:
            system_prompt += f"\n\n请基于以下提供的背景知识来回答用户问题。如果背景知识中没有相关信息，请明确说明并给出你的一般性建议。\n{context_content}"
        
        # 4. 准备API消息
        api_messages = [{"role": "system", "content": system_prompt}]
        
        # 添加历史消息（不包括最后一条用户消息）
        for msg in request.messages[:-1]:
            api_messages.append({
                "role": msg.role,
                "content": msg.content
            })
        
        # 添加当前用户消息
        api_messages.append({
            "role": "user", 
            "content": user_message
        })
        
        # 5. 处理对话
        if request.stream:
            # 流式响应
            async def generate_rag_stream():
                try:
                    # 获取模型配置
                    model_config = get_model_config(request.modelId, db)
                    
                    # 获取或创建对话
                    conversation_id = request.conversationId
                    if not conversation_id:
                        # 创建新对话
                        conversation = create_conversation_with_rag(
                            user_id=DEFAULT_USER_ID,
                            model_id=request.modelId,
                            title=request.title or f"RAG对话 - {datetime.now().strftime('%m-%d %H:%M')}",
                            rag_enabled=True,
                            rag_repository_id=request.repositoryId,
                            rag_top_k=request.ragTopK,
                            rag_threshold=request.ragThreshold,
                            db=db
                        )
                        conversation_id = str(conversation.id)
                        yield f"data: {json.dumps({'conversationId': conversation_id}, ensure_ascii=False)}\n\n"
                    
                    # 保存用户消息
                    user_msg = save_message(
                        conversation_id=conversation_id,
                        role="user",
                        content=user_message,
                        model_id=request.modelId,
                        msg_metadata={
                            "rag_enabled": True,
                            "repository_id": request.repositoryId,
                            "search_results_count": len(search_results),
                            "search_threshold": request.ragThreshold
                        },
                        db=db
                    )
                    
                    # 流式生成回复
                    full_response = ""
                    async for chunk in stream_chat_response(api_messages, model_config):
                        if chunk:
                            full_response += chunk
                            yield f"data: {json.dumps({'content': chunk}, ensure_ascii=False)}\n\n"
                    
                    # 保存助手回复
                    assistant_metadata = {
                        "rag_enabled": True,
                        "repository_id": request.repositoryId,
                        "sources": [
                            {
                                "document_id": r["document_id"],
                                "document_filename": r["document_filename"],
                                "relevance_score": r["relevance_score"],
                                "chunk_index": r["chunk_index"]
                            } for r in search_results
                        ]
                    }
                    
                    assistant_msg = save_message(
                        conversation_id=conversation_id,
                        role="assistant",
                        content=full_response,
                        model_id=request.modelId,
                        msg_metadata=assistant_metadata,
                        db=db
                    )
                    
                    yield f"data: [DONE]\n\n"
                    
                except Exception as e:
                    yield f"data: {json.dumps({'error': str(e)}, ensure_ascii=False)}\n\n"
            
            return StreamingResponse(
                generate_rag_stream(),
                media_type="text/plain",
                headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
            )
        
        else:
            # 非流式响应
            model_config = get_model_config(request.modelId, db)
            response_content = ""
            
            async for chunk in stream_chat_response(api_messages, model_config):
                if chunk:
                    response_content += chunk
            
            # 保存对话记录
            if request.conversationId:
                conversation_id = request.conversationId
            else:
                conversation = create_conversation_with_rag(
                    user_id=DEFAULT_USER_ID,
                    model_id=request.modelId,
                    title=request.title or f"RAG对话 - {datetime.now().strftime('%m-%d %H:%M')}",
                    rag_enabled=True,
                    rag_repository_id=request.repositoryId,
                    rag_top_k=request.ragTopK,
                    rag_threshold=request.ragThreshold,
                    db=db
                )
                conversation_id = str(conversation.id)
            
            # 保存消息
            user_msg = save_message(
                conversation_id=conversation_id,
                role="user",
                content=user_message,
                model_id=request.modelId,
                msg_metadata={
                    "rag_enabled": True,
                    "repository_id": request.repositoryId,
                    "search_results_count": len(search_results)
                },
                db=db
            )
            
            assistant_metadata = {
                "rag_enabled": True,
                "repository_id": request.repositoryId,
                "sources": [
                    {
                        "document_id": r["document_id"],
                        "document_filename": r["document_filename"],
                        "relevance_score": r["relevance_score"]
                    } for r in search_results
                ]
            }
            
            assistant_msg = save_message(
                conversation_id=conversation_id,
                role="assistant",
                content=response_content,
                model_id=request.modelId,
                msg_metadata=assistant_metadata,
                db=db
            )
            
            return {
                "conversationId": conversation_id,
                "content": response_content,
                "model": request.modelId,
                "sources": search_results
            }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"RAG对话失败: {str(e)}")

# 获取所有模型配置
@app.get("/api/models")
async def get_models(db: Session = Depends(get_db)):
    configs = db.query(DBModelConfig).filter(DBModelConfig.enabled == True).all()
    safe_configs = []
    for config in configs:
        safe_configs.append({
            "id": str(config.id),
            "name": config.name,
            "provider": config.provider,
            "apiEndpoint": config.api_endpoint,
            "apiKey": "••••••••",  # 不返回真实密钥
            "model": config.model,
            "enabled": config.enabled,
            "parameters": config.parameters
        })
    return safe_configs

# 添加模型配置
@app.post("/api/models")
async def add_model(model: ModelConfigRequest, db: Session = Depends(get_db)):
    # 获取默认用户
    user = db.query(DBUser).filter(DBUser.id == DEFAULT_USER_ID).first()
    if not user:
        raise HTTPException(status_code=404, detail="用户未找到")
    
    new_model = DBModelConfig(
        name=model.name,
        provider=model.provider,
        api_endpoint=model.apiEndpoint,
        api_key_encrypted=model.apiKey,  # 生产环境需要加密
        model=model.model,
        enabled=model.enabled,
        parameters=model.parameters.dict() if hasattr(model.parameters, 'dict') else model.parameters.model_dump(),
        created_by=user.id
    )
    
    db.add(new_model)
    db.commit()
    db.refresh(new_model)
    
    return {"success": True, "id": str(new_model.id)}

# 更新模型配置
@app.put("/api/models/{model_id}")
async def update_model(model_id: str, model: ModelConfigRequest, db: Session = Depends(get_db)):
    config = db.query(DBModelConfig).filter(DBModelConfig.id == model_id).first()
    if not config:
        raise HTTPException(status_code=404, detail="模型未找到")
    
    # 更新字段
    config.name = model.name
    config.provider = model.provider
    config.api_endpoint = model.apiEndpoint
    if model.apiKey and model.apiKey != "••••••••":
        config.api_key_encrypted = model.apiKey
    config.model = model.model
    config.enabled = model.enabled
    config.parameters = model.parameters.model_dump()
    
    db.commit()
    return {"success": True}

# 删除模型配置
@app.delete("/api/models/{model_id}")
async def delete_model(model_id: str, db: Session = Depends(get_db)):
    config = db.query(DBModelConfig).filter(DBModelConfig.id == model_id).first()
    if not config:
        raise HTTPException(status_code=404, detail="模型未找到")
    
    db.delete(config)
    db.commit()
    return {"success": True}

# 获取对话列表
@app.get("/api/conversations")
async def get_conversations(db: Session = Depends(get_db), limit: int = 50, offset: int = 0):
    conversations = db.query(DBConversation).filter(
        and_(DBConversation.user_id == DEFAULT_USER_ID, DBConversation.is_archived == False)
    ).order_by(DBConversation.updated_at.desc()).offset(offset).limit(limit).all()
    
    result = []
    for conv in conversations:
        result.append({
            "id": str(conv.id),
            "title": conv.title,
            "modelId": str(conv.model_id) if conv.model_id else None,
            "messageCount": conv.message_count,
            "createdAt": conv.created_at.isoformat(),
            "updatedAt": conv.updated_at.isoformat()
        })
    
    return result

# 帮助函数：创建对话（支持RAG参数）
def create_conversation_with_rag(user_id: str, model_id: str, title: str, db: Session, 
                                rag_enabled: bool = False, rag_repository_id: str = None,
                                rag_top_k: int = 5, rag_threshold: float = 0.7,
                                system_prompt: str = None) -> DBConversation:
    """创建新对话，支持RAG参数"""
    conversation = DBConversation(
        user_id=user_id,
        model_id=model_id,
        title=title,
        rag_enabled=rag_enabled,
        rag_repository_id=rag_repository_id,
        rag_top_k=rag_top_k,
        rag_threshold=rag_threshold,
        system_prompt=system_prompt
    )
    
    db.add(conversation)
    db.commit()
    db.refresh(conversation)
    
    return conversation

def get_model_config(model_id: str, db: Session) -> Dict[str, Any]:
    """获取模型配置"""
    # 兼容旧的字符串ID
    if model_id == "qwen":
        model_id = "550e8400-e29b-41d4-a716-************"
    
    model = db.query(DBModelConfig).filter(DBModelConfig.id == model_id).first()
    if not model:
        raise HTTPException(status_code=404, detail="模型未找到")
    
    if not model.enabled:
        raise HTTPException(status_code=400, detail="模型已禁用")
    
    return {
        "id": str(model.id),
        "name": model.name,
        "provider": model.provider,
        "apiEndpoint": model.api_endpoint,
        "apiKey": model.api_key,
        "model": model.model,
        "parameters": {
            "temperature": model.temperature,
            "max_tokens": model.max_tokens,
            "top_p": model.top_p
        }
    }

def save_message(conversation_id: str, role: str, content: str, model_id: str, 
                db: Session, msg_metadata: Dict = None) -> DBMessage:
    """保存消息到数据库"""
    # 处理内容
    content_info = ContentHandler.process_content(content, role)
    
    # 处理元数据
    processed_metadata = {}
    if msg_metadata:
        processed_metadata = MetadataHandler.process_metadata(msg_metadata)
    
    # 创建消息
    message = DBMessage(
        conversation_id=conversation_id,
        role=role,
        content=content_info["processed_content"],
        model_id=model_id,
        token_count=len(content) // 4,  # 简单估算
        msg_metadata=processed_metadata
    )
    
    db.add(message)
    
    # 更新对话统计
    conversation = db.query(DBConversation).filter(DBConversation.id == conversation_id).first()
    if conversation:
        conversation.message_count += 1
        conversation.updated_at = datetime.now()
    
    db.commit()
    db.refresh(message)
    
    return message

# 创建新对话
@app.post("/api/conversations")
async def create_conversation(
    title: str = "新对话",
    model_id: Optional[str] = None,
    system_prompt: Optional[str] = None,
    db: Session = Depends(get_db)
):
    user = db.query(DBUser).filter(DBUser.id == DEFAULT_USER_ID).first()
    if not user:
        raise HTTPException(status_code=404, detail="用户未找到")
    
    new_conversation = DBConversation(
        user_id=user.id,
        title=title,
        model_id=model_id if model_id else None,
        system_prompt=system_prompt
    )
    
    db.add(new_conversation)
    db.commit()
    db.refresh(new_conversation)
    
    return {
        "success": True,
        "conversation": {
            "id": str(new_conversation.id),
            "title": new_conversation.title,
            "modelId": str(new_conversation.model_id) if new_conversation.model_id else None,
            "systemPrompt": new_conversation.system_prompt,
            "createdAt": new_conversation.created_at.isoformat()
        }
    }

# 获取对话详情和消息历史
@app.get("/api/conversations/{conversation_id}")
async def get_conversation(conversation_id: str, db: Session = Depends(get_db)):
    conversation = db.query(DBConversation).filter(DBConversation.id == conversation_id).first()
    if not conversation:
        raise HTTPException(status_code=404, detail="对话未找到")
    
    messages = db.query(DBMessage).filter(DBMessage.conversation_id == conversation_id).order_by(DBMessage.created_at).all()
    
    return {
        "id": str(conversation.id),
        "title": conversation.title,
        "modelId": str(conversation.model_id) if conversation.model_id else None,
        "systemPrompt": conversation.system_prompt,
        "messages": [
            {
                "id": str(msg.id),
                "role": msg.role,
                "content": msg.content,
                "createdAt": msg.created_at.isoformat(),
                "tokenCount": msg.token_count
            }
            for msg in messages
        ],
        "createdAt": conversation.created_at.isoformat(),
        "updatedAt": conversation.updated_at.isoformat()
    }

# 删除对话
@app.delete("/api/conversations/{conversation_id}")
async def delete_conversation(conversation_id: str, db: Session = Depends(get_db)):
    conversation = db.query(DBConversation).filter(DBConversation.id == conversation_id).first()
    if not conversation:
        raise HTTPException(status_code=404, detail="对话未找到")
    
    # 清理上下文缓存
    context = await ContextManager.get_or_create_context(conversation_id)
    await context.clear_context()
    
    db.delete(conversation)
    db.commit()
    return {"success": True}

# 聊天接口
@app.post("/api/chat")
async def chat(request: ChatRequest, db: Session = Depends(get_db)):
    conversation = None
    
    try:
        # 兼容旧的字符串ID，转换为UUID
        model_id = request.modelId
        if model_id == "qwen":
            model_id = "550e8400-e29b-41d4-a716-************"
        
        # 查找模型配置
        model_config = db.query(DBModelConfig).filter(DBModelConfig.id == model_id).first()
        if not model_config:
            raise HTTPException(status_code=404, detail="模型未找到")
        
        if not model_config.enabled:
            raise HTTPException(status_code=400, detail="模型已禁用")
        
        # 获取或创建对话
        if request.conversationId:
            # 检查对话ID格式，如果不是UUID格式，直接创建新对话
            try:
                # 尝试解析为UUID，如果失败说明是前端的时间戳ID
                import uuid
                uuid.UUID(request.conversationId)
                # 如果能解析为UUID，则尝试查找
                conversation = db.query(DBConversation).filter(DBConversation.id == request.conversationId).first()
            except (ValueError, Exception) as conv_error:
                print(f"对话ID格式无效或查找失败 ('{request.conversationId}'): {conv_error}")
                conversation = None
        
        if not conversation:
            # 创建新对话
            user = db.query(DBUser).filter(DBUser.id == DEFAULT_USER_ID).first()
            if not user:
                # 如果默认用户不存在，创建一个
                user = DBUser(
                    id=DEFAULT_USER_ID,
                    username="默认用户",
                    email="<EMAIL>"
                )
                db.add(user)
                db.flush()  # 使用flush而不是commit
                print(f"创建默认用户: {user.id}")
                
            conversation = DBConversation(
                user_id=user.id,
                title=request.title or "新对话",
                model_id=model_config.id,
                system_prompt=request.systemPrompt
            )
            db.add(conversation)
            db.commit()  # 立即commit确保对话存在
            db.refresh(conversation)
            print(f"创建新对话: {conversation.id}")
        
        # 获取上下文管理器
        context = await ContextManager.get_or_create_context(str(conversation.id))
        
        # 如果是现有对话，从数据库加载历史消息到上下文
        if request.conversationId:
            # 检查上下文是否为空（说明是新会话或缓存过期）
            existing_context = await context.get_context(include_system=False)
            if not existing_context:
                # 从数据库加载历史消息
                historical_messages = db.query(DBMessage).filter(
                    DBMessage.conversation_id == conversation.id
                ).order_by(DBMessage.created_at).all()
                
                # 将历史消息添加到上下文
                for db_msg in historical_messages:
                    await context.add_message(ChatMessage(
                        role=db_msg.role,
                        content=db_msg.content,
                        timestamp=db_msg.created_at,
                        token_count=db_msg.token_count or len(db_msg.content) // 4
                    ))
        
        # 添加用户消息到上下文和数据库
        user_message = request.messages[-1]  # 获取最新的用户消息
        await context.add_message(ChatMessage(
            role=user_message.role,
            content=user_message.content,
            token_count=len(user_message.content) // 4  # 简单估算
        ))
        
        # 获取API调用的消息列表（包含历史上下文）
        api_messages = await context.get_messages_for_api(conversation.system_prompt)
        
        # 处理用户消息内容
        content_info = ContentHandler.process_content(user_message.content, "user")
        
        # 保存用户消息到数据库
        user_metadata = {
            "timestamp": datetime.now().isoformat(),
            "client_info": request.dict() if hasattr(request, 'dict') else {},
            "message_length": len(user_message.content),
            "conversation_context": f"Message {len(api_messages) + 1} in conversation",
            "content_info": content_info
        }
        
        db_message = DBMessage(
            conversation_id=conversation.id,
            role=user_message.role,
            content=content_info["content"],  # 使用处理后的内容
            msg_metadata=MetadataHandler.process_metadata(user_metadata),
            token_count=len(user_message.content) // 4  # 基于原始长度计算token
        )
        db.add(db_message)
        db.commit()  # 立即commit确保消息存在
        db.refresh(db_message)
        
        # 调试：打印上下文信息
        print(f"对话ID: {conversation.id}")
        print(f"上下文消息数量: {len(api_messages)}")
        print(f"消息内容: {[msg['role'] + ': ' + msg['content'][:50] + '...' for msg in api_messages[-3:]]}")  # 最近3条消息
        
        # 转换模型配置格式
        config_dict = {
            "id": str(model_config.id),
            "name": model_config.name,
            "provider": model_config.provider,
            "apiEndpoint": model_config.api_endpoint,
            "apiKey": model_config.api_key_encrypted,
            "model": model_config.model,
            "enabled": model_config.enabled,
            "parameters": model_config.parameters
        }
        
        if request.stream:
            return StreamingResponse(
                stream_chat_response_with_context(config_dict, api_messages, context, conversation, db),
                media_type="text/event-stream",
                headers={
                    "Cache-Control": "no-cache",
                    "Connection": "keep-alive",
                    "Access-Control-Allow-Origin": "*",
                }
            )
        else:
            # 非流式响应
            response = await get_chat_response_with_context(config_dict, api_messages, context, conversation, db)
            return response
    
    except HTTPException:
        # HTTP异常不需要回滚，直接抛出
        raise
    except Exception as e:
        print(f"聊天API错误: {e}")
        # 发生任何其他错误时回滚事务
        try:
            db.rollback()
        except:
            pass
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
    
    # 注意：不在这里commit，让流式响应函数或非流式响应函数来commit

# 流式聊天响应生成器（带上下文）
async def stream_chat_response_with_context(
    model_config: Dict[str, Any], 
    messages: List[Dict[str, str]], 
    context: ChatContext,
    conversation: DBConversation,
    db: Session
) -> AsyncGenerator[str, None]:
    assistant_content = ""  # 收集完整的助手回复
    start_time = datetime.now()
    
    try:
        provider = model_config["provider"]
        
        if provider == "dashscope":
            async for chunk in stream_dashscope_request(model_config, messages):
                if chunk.get("content"):
                    assistant_content += chunk["content"]
                chunk["conversationId"] = str(conversation.id)
                yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
        elif provider == "openai":
            async for chunk in stream_openai_request(model_config, messages):
                if chunk.get("content"):
                    assistant_content += chunk["content"]
                    chunk["conversationId"] = str(conversation.id)
                yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
        elif provider == "anthropic":
            async for chunk in stream_anthropic_request(model_config, messages):
                if chunk.get("content"):
                    assistant_content += chunk["content"]
                    chunk["conversationId"] = str(conversation.id)
                yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
        else:
            yield f"data: {json.dumps({'error': '不支持的模型提供商'}, ensure_ascii=False)}\n\n"
            return
        
        # 流式输出完成后，保存助手回复到上下文和数据库
        if assistant_content:
            try:
                await context.add_message(ChatMessage(
                    role="assistant",
                    content=assistant_content,
                    token_count=len(assistant_content) // 4
                ))
                
                # 处理助手消息内容
                assistant_content_info = ContentHandler.process_content(assistant_content, "assistant")
                
                # 保存到数据库
                assistant_metadata = {
                    "timestamp": datetime.now().isoformat(),
                    "model_info": {
                        "provider": model_config["provider"],
                        "model": model_config["model"],
                        "parameters": model_config.get("parameters", {})
                    },
                    "response_time_ms": int((datetime.now() - start_time).total_seconds() * 1000),
                    "content_length": len(assistant_content),
                    "streaming": True,
                    "content_info": assistant_content_info
                }
                
                db_message = DBMessage(
                    conversation_id=conversation.id,
                    role="assistant",
                    content=assistant_content_info["content"],  # 使用处理后的内容
                    msg_metadata=MetadataHandler.process_metadata(assistant_metadata),
                    token_count=len(assistant_content) // 4  # 基于原始长度计算token
                )
                db.add(db_message)
                
                # 更新对话统计
                response_time = int((datetime.now() - start_time).total_seconds() * 1000)
                statistic = DBChatStatistic(
                    user_id=conversation.user_id,
                    conversation_id=conversation.id,
                    model_id=conversation.model_id,
                    request_tokens=sum(len(msg.get("content", "")) // 4 for msg in messages),
                    response_tokens=len(assistant_content) // 4,
                    response_time_ms=response_time,
                    status="success"
                )
                db.add(statistic)
                
                # 更新对话计数
                conversation.message_count = db.query(DBMessage).filter(DBMessage.conversation_id == conversation.id).count()
                
                db.commit()
                print(f"✅ 流式响应保存成功，对话ID: {conversation.id}")
                
            except Exception as save_error:
                print(f"❌ 保存流式响应失败: {save_error}")
                try:
                    db.rollback()
                except:
                    pass
    
    except Exception as e:
        print(f"流式聊天错误: {e}")
        # 记录错误统计
        try:
            response_time = int((datetime.now() - start_time).total_seconds() * 1000)
            statistic = DBChatStatistic(
                user_id=conversation.user_id,
                conversation_id=conversation.id,
                model_id=conversation.model_id,
                response_time_ms=response_time,
                status="error"
            )
            db.add(statistic)
            db.commit()
        except Exception as db_error:
            print(f"记录错误统计失败: {db_error}")
            db.rollback()
        
        # 降级到普通请求并模拟流式输出
        try:
            response = await get_chat_response_with_context(model_config, messages, context, conversation, db)
            content = response["content"]
            
            # 模拟流式输出
            for i in range(0, len(content), 2):
                chunk_content = content[i:i+2]
                chunk = {
                    "content": chunk_content,
                    "model": model_config["name"],
                    "conversationId": str(conversation.id)
                }
                yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
                await asyncio.sleep(0.05)  # 50ms延迟
        except Exception as fallback_error:
            yield f"data: {json.dumps({'error': f'服务器错误: {str(fallback_error)}'}, ensure_ascii=False)}\n\n"
    
    yield "data: [DONE]\n\n"

# 带上下文的普通响应获取
async def get_chat_response_with_context(
    model_config: Dict[str, Any], 
    messages: List[Dict[str, str]], 
    context: ChatContext,
    conversation: DBConversation,
    db: Session
) -> Dict[str, Any]:
    start_time = datetime.now()
    
    try:
        # 调用原有的获取响应函数
        response = await get_chat_response(model_config, messages)
        
        # 保存助手回复到上下文和数据库
        assistant_content = response["content"]
        await context.add_message(ChatMessage(
            role="assistant",
            content=assistant_content,
            token_count=len(assistant_content) // 4
        ))
        
        # 处理助手消息内容
        assistant_content_info = ContentHandler.process_content(assistant_content, "assistant")
        
        # 保存到数据库
        assistant_metadata = {
            "timestamp": datetime.now().isoformat(),
            "model_info": {
                "provider": model_config["provider"],
                "model": model_config["model"],
                "parameters": model_config.get("parameters", {})
            },
            "response_time_ms": int((datetime.now() - start_time).total_seconds() * 1000),
            "content_length": len(assistant_content),
            "streaming": False,
            "usage": response.get("usage", {}),
            "content_info": assistant_content_info
        }
        
        db_message = DBMessage(
            conversation_id=conversation.id,
            role="assistant",
            content=assistant_content_info["content"],  # 使用处理后的内容
            msg_metadata=MetadataHandler.process_metadata(assistant_metadata),
            token_count=len(assistant_content) // 4  # 基于原始长度计算token
        )
        db.add(db_message)
        
        # 更新对话统计
        response_time = int((datetime.now() - start_time).total_seconds() * 1000)
        statistic = DBChatStatistic(
            user_id=conversation.user_id,
            conversation_id=conversation.id,
            model_id=conversation.model_id,
            request_tokens=sum(len(msg.get("content", "")) // 4 for msg in messages),
            response_tokens=len(assistant_content) // 4,
            response_time_ms=response_time,
            status="success"
        )
        db.add(statistic)
        
        # 更新对话计数
        conversation.message_count = db.query(DBMessage).filter(DBMessage.conversation_id == conversation.id).count()
        
        db.commit()
        print(f"✅ 非流式响应保存成功，对话ID: {conversation.id}")
        
        # 添加对话ID到响应
        response["conversationId"] = str(conversation.id)
        
        return response
        
    except Exception as e:
        # 记录错误统计
        try:
            response_time = int((datetime.now() - start_time).total_seconds() * 1000)
            statistic = DBChatStatistic(
                user_id=conversation.user_id,
                conversation_id=conversation.id,
                model_id=conversation.model_id,
                response_time_ms=response_time,
                status="error"
            )
            db.add(statistic)
            db.commit()
        except Exception as stat_error:
            print(f"记录错误统计失败: {stat_error}")
            try:
                db.rollback()
            except:
                pass
        
        raise e

# 流式聊天响应生成器（原有函数，保持兼容性）
async def stream_chat_response(model_config: Dict[str, Any], messages) -> AsyncGenerator[str, None]:
    provider = model_config["provider"]
    
    if provider == "dashscope":
        async for chunk in stream_dashscope_request(model_config, messages):
            yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
    elif provider == "openai":
        async for chunk in stream_openai_request(model_config, messages):
            yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
    elif provider == "anthropic":
        async for chunk in stream_anthropic_request(model_config, messages):
            yield f"data: {json.dumps(chunk, ensure_ascii=False)}\n\n"
    else:
        yield f"data: {json.dumps({'error': '不支持的模型提供商'}, ensure_ascii=False)}\n\n"

# 通义千问流式请求
async def stream_dashscope_request(model_config: Dict[str, Any], messages) -> AsyncGenerator[Dict[str, Any], None]:
    # 兼容两种消息格式：Dict 和 MessageRequest
    if messages and hasattr(messages[0], 'role'):
        # MessageRequest 格式
        message_list = [{"role": msg.role, "content": msg.content} for msg in messages]
    else:
        # Dict 格式
        message_list = [{"role": msg["role"], "content": msg["content"]} for msg in messages]
    
    request_data = {
        "model": model_config["model"],
        "input": {
            "messages": message_list
        },
        "parameters": {
            "temperature": model_config["parameters"]["temperature"],
            "max_tokens": model_config["parameters"]["max_tokens"],
            "top_p": model_config["parameters"]["top_p"],
            "result_format": "message",
            "incremental_output": True
        }
    }
    
    headers = {
        "Authorization": f"Bearer {model_config['apiKey']}",
        "Content-Type": "application/json",
        "Accept": "text/event-stream"
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            async with client.stream(
                "POST", 
                model_config["apiEndpoint"], 
                json=request_data, 
                headers=headers
            ) as response:
                if response.status_code != 200:
                    raise Exception(f"API请求失败: {response.status_code}")
                
                buffer = ""
                async for chunk in response.aiter_text():
                    buffer += chunk
                    lines = buffer.split('\n')
                    buffer = lines[-1]  # 保留最后一个不完整的行
                    
                    for line in lines[:-1]:
                        line = line.strip()
                        if line.startswith('data:'):
                            data = line[5:].strip()
                            if data == '[DONE]':
                                return
                            
                            try:
                                parsed = json.loads(data)
                                if (parsed.get("output") and 
                                    parsed["output"].get("choices") and 
                                    len(parsed["output"]["choices"]) > 0):
                                    choice = parsed["output"]["choices"][0]
                                    if choice.get("message") and choice["message"].get("content"):
                                        chunk_content = choice["message"]["content"]
                                        yield {
                                            "content": chunk_content,
                                            "model": model_config["name"],
                                            "usage": parsed.get("usage")
                                        }
                            except json.JSONDecodeError:
                                continue
    
    except Exception as e:
        # 降级到普通请求
        print(f"通义千问流式请求失败，降级到普通请求: {e}")
        response = await get_dashscope_response(model_config, messages)
        content = response["content"]
        
        # 模拟流式输出
        for i in range(0, len(content), 1):
            yield {
                "content": content[i],
                "model": model_config["name"],
                "usage": response.get("usage") if i == len(content) - 1 else None
            }
            await asyncio.sleep(0.03)

# OpenAI流式请求
async def stream_openai_request(model_config: Dict[str, Any], messages) -> AsyncGenerator[Dict[str, Any], None]:
    # 兼容两种消息格式：Dict 和 MessageRequest
    if messages and hasattr(messages[0], 'role'):
        # MessageRequest 格式
        message_list = [{"role": msg.role, "content": msg.content} for msg in messages]
    else:
        # Dict 格式
        message_list = [{"role": msg["role"], "content": msg["content"]} for msg in messages]
    
    request_data = {
        "model": model_config["model"],
        "messages": message_list,
        "temperature": model_config["parameters"]["temperature"],
        "max_tokens": model_config["parameters"]["max_tokens"],
        "stream": True
    }
    
    headers = {
        "Authorization": f"Bearer {model_config['apiKey']}",
        "Content-Type": "application/json"
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            async with client.stream(
                "POST", 
                model_config["apiEndpoint"], 
                json=request_data, 
                headers=headers
            ) as response:
                buffer = ""
                async for chunk in response.aiter_text():
                    buffer += chunk
                    lines = buffer.split('\n')
                    buffer = lines[-1]
                    
                    for line in lines[:-1]:
                        line = line.strip()
                        if line.startswith('data: '):
                            data = line[6:].strip()
                            if data == '[DONE]':
                                return
                            
                            try:
                                parsed = json.loads(data)
                                if (parsed.get("choices") and 
                                    len(parsed["choices"]) > 0 and 
                                    parsed["choices"][0].get("delta")):
                                    delta = parsed["choices"][0]["delta"]
                                    if delta.get("content"):
                                        yield {
                                            "content": delta["content"],
                                            "model": model_config["name"],
                                            "usage": parsed.get("usage")
                                        }
                            except json.JSONDecodeError:
                                continue
    
    except Exception as e:
        print(f"OpenAI流式请求失败: {e}")
        # 这里可以添加降级逻辑

# Anthropic流式请求
async def stream_anthropic_request(model_config: Dict[str, Any], messages) -> AsyncGenerator[Dict[str, Any], None]:
    # 兼容两种消息格式：Dict 和 MessageRequest
    if messages and hasattr(messages[0], 'role'):
        # MessageRequest 格式
        message_list = [{"role": msg.role, "content": msg.content} for msg in messages]
    else:
        # Dict 格式
        message_list = [{"role": msg["role"], "content": msg["content"]} for msg in messages]
    
    request_data = {
        "model": model_config["model"],
        "messages": message_list,
        "max_tokens": model_config["parameters"]["max_tokens"],
        "stream": True
    }
    
    headers = {
        "x-api-key": model_config["apiKey"],
        "Content-Type": "application/json",
        "anthropic-version": "2023-06-01"
    }
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            async with client.stream(
                "POST", 
                model_config["apiEndpoint"], 
                json=request_data, 
                headers=headers
            ) as response:
                buffer = ""
                async for chunk in response.aiter_text():
                    buffer += chunk
                    lines = buffer.split('\n')
                    buffer = lines[-1]
                    
                    for line in lines[:-1]:
                        line = line.strip()
                        if line.startswith('data: '):
                            data = line[6:].strip()
                            if data == '[DONE]':
                                return
                            
                            try:
                                parsed = json.loads(data)
                                if (parsed.get("type") == "content_block_delta" and 
                                    parsed.get("delta") and 
                                    parsed["delta"].get("text")):
                                    yield {
                                        "content": parsed["delta"]["text"],
                                        "model": model_config["name"]
                                    }
                            except json.JSONDecodeError:
                                continue
    
    except Exception as e:
        print(f"Anthropic流式请求失败: {e}")

# 非流式响应获取
async def get_chat_response(model_config: Dict[str, Any], messages) -> Dict[str, Any]:
    provider = model_config["provider"]
    
    if provider == "dashscope":
        return await get_dashscope_response(model_config, messages)
    elif provider == "openai":
        return await get_openai_response(model_config, messages)
    elif provider == "anthropic":
        return await get_anthropic_response(model_config, messages)
    else:
        raise HTTPException(status_code=400, detail="不支持的模型提供商")

# 通义千问普通请求
async def get_dashscope_response(model_config: Dict[str, Any], messages) -> Dict[str, Any]:
    # 兼容两种消息格式：Dict 和 MessageRequest
    if messages and hasattr(messages[0], 'role'):
        # MessageRequest 格式
        message_list = [{"role": msg.role, "content": msg.content} for msg in messages]
    else:
        # Dict 格式
        message_list = [{"role": msg["role"], "content": msg["content"]} for msg in messages]
    
    request_data = {
        "model": model_config["model"],
        "input": {
            "messages": message_list
        },
        "parameters": {
            "temperature": model_config["parameters"]["temperature"],
            "max_tokens": model_config["parameters"]["max_tokens"],
            "top_p": model_config["parameters"]["top_p"],
            "result_format": "message"
        }
    }
    
    headers = {
        "Authorization": f"Bearer {model_config['apiKey']}",
        "Content-Type": "application/json"
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        response = await client.post(
            model_config["apiEndpoint"], 
            json=request_data, 
            headers=headers
        )
        
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail=f"API请求失败: {response.text}")
        
        data = response.json()
        return {
            "content": data["output"]["choices"][0]["message"]["content"],
            "model": model_config["name"],
            "usage": data.get("usage")
        }

# OpenAI普通请求
async def get_openai_response(model_config: Dict[str, Any], messages) -> Dict[str, Any]:
    # 兼容两种消息格式：Dict 和 MessageRequest
    if messages and hasattr(messages[0], 'role'):
        # MessageRequest 格式
        message_list = [{"role": msg.role, "content": msg.content} for msg in messages]
    else:
        # Dict 格式
        message_list = [{"role": msg["role"], "content": msg["content"]} for msg in messages]
    
    request_data = {
        "model": model_config["model"],
        "messages": message_list,
        "temperature": model_config["parameters"]["temperature"],
        "max_tokens": model_config["parameters"]["max_tokens"]
    }
    
    headers = {
        "Authorization": f"Bearer {model_config['apiKey']}",
        "Content-Type": "application/json"
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        response = await client.post(
            model_config["apiEndpoint"], 
            json=request_data, 
            headers=headers
        )
        
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail=f"API请求失败: {response.text}")
        
        data = response.json()
        return {
            "content": data["choices"][0]["message"]["content"],
            "model": model_config["name"],
            "usage": data.get("usage")
        }

# Anthropic普通请求
async def get_anthropic_response(model_config: Dict[str, Any], messages) -> Dict[str, Any]:
    # 兼容两种消息格式：Dict 和 MessageRequest
    if messages and hasattr(messages[0], 'role'):
        # MessageRequest 格式
        message_list = [{"role": msg.role, "content": msg.content} for msg in messages]
    else:
        # Dict 格式
        message_list = [{"role": msg["role"], "content": msg["content"]} for msg in messages]
    
    request_data = {
        "model": model_config["model"],
        "messages": message_list,
        "max_tokens": model_config["parameters"]["max_tokens"]
    }
    
    headers = {
        "x-api-key": model_config["apiKey"],
        "Content-Type": "application/json",
        "anthropic-version": "2023-06-01"
    }
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        response = await client.post(
            model_config["apiEndpoint"], 
            json=request_data, 
            headers=headers
        )
        
        if response.status_code != 200:
            raise HTTPException(status_code=response.status_code, detail=f"API请求失败: {response.text}")
        
        data = response.json()
        return {
            "content": data["content"][0]["text"],
            "model": model_config["name"],
            "usage": data.get("usage")
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=3001) 